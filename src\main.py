import asyncio
import argparse
import json
from pathlib import Path
import time
from typing import Dict, List, Any, Optional, Tuple, Union
import httpx
import statistics
import traceback
import re
import warnings
import os
import mimetypes
import base64
import datetime
import collections
import io
import aiofiles
import csv

class TimingResult:
    """Stocke les résultats de chronométrage pour un appel HTTP."""
    def __init__(self, endpoint: str, thread_id: int, start_time: float, 
                 end_time: float, status_code: int, endpoint_id: Optional[str] = None):
        self.endpoint = endpoint
        self.endpoint_id = endpoint_id or endpoint  # Utilise l'ID personnalisé ou l'URL par défaut
        self.thread_id = thread_id
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.status_code = status_code
    
    def __str__(self):
        if self.endpoint_id != self.endpoint:
            return f"Thread {self.thread_id} - {self.endpoint} (ID: {self.endpoint_id}): {self.duration:.4f}s (Status: {self.status_code})"
        return f"Thread {self.thread_id} - {self.endpoint}: {self.duration:.4f}s (Status: {self.status_code})"

class Timer:
    """Gère le chronométrage global et par appel."""
    def __init__(self):
        self.results: List[TimingResult] = []
        self.global_start: Optional[float] = None
        self.global_end: Optional[float] = None
        # Dictionnaire pour compter les erreurs par code de statut
        self.status_code_counts = collections.defaultdict(int)
        # Dictionnaire pour stocker les URLs par code de statut avec leur nombre d'occurrences
        self.status_urls = collections.defaultdict(lambda: collections.defaultdict(int))
        # Buffer pour stocker la sortie du rapport
        self.report_buffer = io.StringIO()
        # Nombre de threads et tâches
        self.num_threads = 0
        self.num_tasks_per_thread = 0
        # Attribut pour le fichier de log d'erreur
        self.error_log_file = None

    def set_thread_and_task_info(self, num_threads: int, num_tasks_per_thread: int):
        """Définit le nombre de threads et de tâches par thread."""
        self.num_threads = num_threads
        self.num_tasks_per_thread = num_tasks_per_thread

    def start_global(self):
        self.global_start = time.time()
        # Initialiser le fichier de log d'erreur unique pour ce run
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.error_log_file = logs_dir / f"errors_{timestamp}.log"

    def end_global(self):
        self.global_end = time.time()
    
    def add_result(self, result: TimingResult):
        self.results.append(result)
        # Compter le code de statut
        self.status_code_counts[result.status_code] += 1
        # Stocker l'URL pour ce code de statut s'il s'agit d'une erreur
        if result.status_code >= 400 or result.status_code < 0:
            # Extraire la partie de l'URL avant le "?"
            clean_url = result.endpoint.split('?')[0]
            # Incrémenter le compteur pour cette URL
            self.status_urls[result.status_code][clean_url] += 1

    def _write_to_output(self, text: str):
        """Écrit le texte à la fois dans le buffer du rapport et sur la console."""
        self.report_buffer.write(text + "\n")

    def _print_global_info(self):
        """Affiche les informations globales du test."""
        global_time = self.global_end - self.global_start if self.global_start and self.global_end else 0
        
        self._write_to_output("\n===== RÉSUMÉ DU TEST =====")
        self._write_to_output(f"Temps d'exécution total: {global_time:.4f}s")
        self._write_to_output(f"Nombre total de requêtes: {len(self.results)}")
        self._write_to_output(f"Nombre de threads: {self.num_threads}")
        self._write_to_output(f"Nombre de tâches par thread: {self.num_tasks_per_thread}")
        self._write_to_output(f"Nombre total de tâches: {self.num_threads * self.num_tasks_per_thread}")

    def _print_endpoint_statistics(self):
        """Calcule et affiche les statistiques par endpoint."""
        # Regrouper les durées par endpoint_id
        endpoints_data = self._group_results_by_endpoint()

        self._write_to_output("\n--- STATISTIQUES PAR ENDPOINT ---")
        for endpoint_id, durations in endpoints_data.items():
            self._print_duration_statistics(endpoint_id, durations)

    def _group_results_by_endpoint(self) -> Dict[str, List[float]]:
        """Regroupe les résultats par endpoint_id."""
        endpoints = {}
        for result in self.results:
            if result.endpoint_id not in endpoints:
                endpoints[result.endpoint_id] = []
            endpoints[result.endpoint_id].append(result.duration) # pyright: ignore[reportUnknownMemberType]
        return endpoints # pyright: ignore[reportUnknownVariableType]

    def _print_duration_statistics(self, identifier: str, durations: List[float]):
        """Affiche les statistiques de durée pour un identifiant donné, avec percentiles."""
        def percentile(data, perc):
            if not data:
                return 0
            data = sorted(data)
            k = (len(data) - 1) * perc
            f = int(k)
            c = min(f + 1, len(data) - 1)
            if f == c:
                return data[int(k)]
            d0 = data[f] * (c - k)
            d1 = data[c] * (k - f)
            return d0 + d1

        p95 = percentile(durations, 0.95)
        p99 = percentile(durations, 0.99)

        self._write_to_output(f"\n{identifier}:")
        self._write_to_output(f"  Nombre: {len(durations)}")
        self._write_to_output(f"  Min: {min(durations):.4f}s")
        self._write_to_output(f"  Max: {max(durations):.4f}s")
        self._write_to_output(f"  Moyenne: {statistics.mean(durations):.4f}s")
        self._write_to_output(f"  Médiane: {statistics.median(durations):.4f}s")
        self._write_to_output(f"  p95: {p95:.4f}s")
        self._write_to_output(f"  p99: {p99:.4f}s")
        
    def _print_thread_statistics(self):
        """Calcule et affiche les statistiques par thread."""
        # Regrouper les durées par thread_id
        threads_data = self._group_results_by_thread()

        self._write_to_output("\n--- STATISTIQUES PAR THREAD ---")
        for thread_id, durations in threads_data.items():
            total_duration = sum(durations)
            self._write_to_output(f"Thread {thread_id}: {len(durations)} requêtes en {total_duration:.4f}s")

    def _group_results_by_thread(self) -> Dict[int, List[float]]:
        """Regroupe les résultats par thread_id."""
        threads = {}
        for result in self.results:
            if result.thread_id not in threads:
                threads[result.thread_id] = []
            threads[result.thread_id].append(result.duration)
        return threads

    def _print_http_status_statistics(self):
        """Affiche les statistiques des codes de statut HTTP."""
        self._write_to_output("\n--- STATISTIQUES DES CODES HTTP ---")

        # Classer les codes de statut par catégorie
        categories = self._categorize_status_codes()

        # Afficher chaque catégorie non vide
        self._print_status_categories(categories)

    def _categorize_status_codes(self) -> Dict[str, List[str]]:
        """Classe les codes de statut HTTP par catégorie."""
        categories = {
            "Succès (2xx)": [],
            "Redirection (3xx)": [],
            "Erreur client (4xx)": [],
            "Erreur serveur (5xx)": [],
            "Erreurs de connexion": []
        }

        # Trier les codes de statut pour un affichage cohérent
        sorted_status_codes = sorted(self.status_code_counts.items())

        for code, count in sorted_status_codes:
            urls_dict = self.status_urls.get(code, {})
            if code < 0:
                categories["Erreurs de connexion"].append(
                    self._format_error_code(code, count, urls_dict)
                )
            elif 200 <= code < 300:
                categories["Succès (2xx)"].append(
                    self._format_status_code(code, count)
                )
            elif 300 <= code < 400:
                categories["Redirection (3xx)"].append(
                    self._format_status_code(code, count)
                )
            elif 400 <= code < 500:
                categories["Erreur client (4xx)"].append(
                    self._format_status_code(code, count, urls_dict)
                )
            elif 500 <= code < 600:
                categories["Erreur serveur (5xx)"].append(
                    self._format_status_code(code, count, urls_dict)
                )

        return categories

    def _format_error_code(self, code: int, count: int, urls_dict: Optional[Dict[str, int]] = None) -> str:
        """Formate un message pour un code d'erreur interne."""
        error_type = "Inconnue"
        if code == -1:
            error_type = "Erreur de requête HTTP"
        elif code == -2:
            error_type = "Exception générale"
        elif code == -3:
            error_type = "Fichier non trouvé"

        message = f"  {error_type} ({code}): {count} occurrences"

        # Ajouter les URLs en erreur si disponibles
        if urls_dict and len(urls_dict) > 0:
            message += "\n    URLs en erreur:"
            for url, url_count in urls_dict.items():
                message += f"\n    - {url} ({url_count} occurrences)"

        return message

    def _format_status_code(self, code: int, count: int, urls_dict: Optional[Dict[str, int]] = None) -> str:
        """Formate un message pour un code de statut HTTP."""
        message = f"  {code}: {count} occurrences"

        # Ajouter les URLs en erreur pour les codes d'erreur (4xx, 5xx)
        if urls_dict and len(urls_dict) > 0 and code >= 400:
            message += "\n    URLs en erreur:"
            for url, url_count in urls_dict.items():
                message += f"\n    - {url} ({url_count} occurrences)"

        return message

    def _print_status_categories(self, categories: Dict[str, List[str]]):
        """Affiche les catégories de codes de statut non vides."""
        for category, items in categories.items():
            if items:
                self._write_to_output(f"\n{category}:")
                for item in items:
                    self._write_to_output(item)

    async def _save_report_to_file(self):
        """Enregistre le rapport dans un fichier avec un timestamp."""
        try:
            # Créer le dossier des rapports s'il n'existe pas
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)

            # Générer un nom de fichier avec timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = reports_dir / f"test_report_{timestamp}.txt"

            # Écrire le contenu du buffer dans le fichier de façon asynchrone
            async with aiofiles.open(report_filename, "w", encoding="utf-8") as f:
                await f.write(self.report_buffer.getvalue())

            self._write_to_output(f"\nRapport enregistré dans le fichier: {report_filename}")
        except Exception as e:
            self._write_to_output(f"\nErreur lors de l'enregistrement du rapport: {str(e)}")

    async def log_error_response(self, endpoint: str, thread_id: int, status_code: int, response_content: str, endpoint_id: Optional[str] = None, url: Optional[str] = None, exception_message: str = ""):
        """Loggue le contenu d'une réponse en erreur dans un fichier séparé, avec l'URL et le type d'exception si disponible."""
        try:
            log_file = getattr(self, 'error_log_file', None)
            if not log_file:
                logs_dir = Path("logs")
                logs_dir.mkdir(exist_ok=True)
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                log_file = logs_dir / f"errors_{timestamp}.log"
                self.error_log_file = log_file
            url_info = f"URL: {url}" if url else ""
            exc_info = f"Type d'exception: {exception_message}" if exception_message else ""
            async with aiofiles.open(log_file, "a", encoding="utf-8") as f:
                await f.write(f"[Thread {thread_id}] [{endpoint_id or endpoint}] [HTTP {status_code}]\n{url_info}\n{exc_info}\n{response_content}\n{'-'*60}\n")
        except Exception as e:
            print(f"Erreur lors de l'écriture du log d'erreur: {e}")

    def _print_failed_endpoints(self):
        """Affiche les endpoints ayant échoué dans le résumé."""
        failed = [r for r in self.results if r.status_code >= 400 or r.status_code < 0]
        if failed:
            self._write_to_output("\n--- ENDPOINTS EN ÉCHEC ---")
            for r in failed:
                self._write_to_output(
                    f"{r.endpoint_id} (Thread {r.thread_id}) - Status: {r.status_code} - Duration: {r.duration:.4f}s"
                )

    def _print_error_categories(self):
        """Affiche un résumé des erreurs par type, y compris Azure OpenAI."""
        error_types = collections.defaultdict(list)
        aoai_errors = []
        for r in self.results:
            if r.status_code < 0:
                error_types["Erreurs internes"].append(r)
            elif 400 <= r.status_code < 500:
                error_types["Erreur client (4xx)"].append(r)
            elif 500 <= r.status_code < 600:
                error_types["Erreur serveur (5xx)"].append(r)
            # Detect AOAI errors by endpoint or error message
            if "aoai" in r.endpoint.lower() or "aoai" in getattr(r, "endpoint_id", "").lower():
                aoai_errors.append(r)
        if error_types:
            self._write_to_output("\n--- CATÉGORIES D'ERREURS ---")
            for cat, items in error_types.items():
                self._write_to_output(f"{cat}: {len(items)}")
                for r in items:
                    self._write_to_output(f"  - {r.endpoint_id} (Thread {r.thread_id}) Status: {r.status_code}")
            if aoai_errors:
                self._write_to_output(f"\nErreurs Azure OpenAI (aoai): {len(aoai_errors)}")
                for r in aoai_errors:
                    self._write_to_output(f"  - {r.endpoint_id} (Thread {r.thread_id}) Status: {r.status_code}")


    async def export_results_json(self, filename="results.json"):
        """Export timing results to JSON."""
        import json
        data = [vars(r) for r in self.results]
        async with aiofiles.open(filename, "w", encoding="utf-8") as f:
            await f.write(json.dumps(data, ensure_ascii=False, indent=2))

    async def export_results_csv(self, filename="results.csv"):
        """Export timing results to CSV."""
        fieldnames = ["endpoint", "endpoint_id", "thread_id", "start_time", "end_time", "duration", "status_code"]
        async with aiofiles.open(filename, "w", encoding="utf-8", newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            await f.write(','.join(fieldnames) + '\n')
            for r in self.results:
                row = [str(getattr(r, k)) for k in fieldnames]
                await f.write(','.join(row) + '\n')

    async def print_summary(self, context_file=None, base_url=None):
        """Affiche un résumé des résultats du test et l'enregistre dans un fichier."""
        if not self.results:
            self._write_to_output("Aucun résultat enregistré")
            return

        # Réinitialiser le buffer de rapport
        self.report_buffer = io.StringIO()

        # Ajout : indiquer le fichier de contexte utilisé
        if context_file:
            self._write_to_output(f"Fichier de contexte utilisé : {context_file}")

        # Afficher les informations globales
        self._print_global_info()

        # Afficher les statistiques par endpoint
        self._print_endpoint_statistics()

        # Afficher les statistiques par thread
        self._print_thread_statistics()

        # Afficher les statistiques des codes HTTP
        self._print_http_status_statistics()

        # Afficher les endpoints en échec
        self._print_failed_endpoints()

        # Afficher les catégories d'erreurs
        self._print_error_categories()

        # Ajout : afficher le lien vers le fichier d'erreur s'il existe et qu'il y a eu des erreurs
        if self.error_log_file and any(
            code >= 400 or code < 0 for code in self.status_code_counts
        ):
            self._write_to_output(f"\nFichier de log des erreurs : {self.error_log_file}")

        # Écrire le rapport dans un fichier
        await self._save_report_to_file()

        # Export JSON/CSV
        await self.export_results_json()
        await self.export_results_csv()

        # Afficher le rapport à la console
        print(self.report_buffer.getvalue())
        
class HttpClient:
    """Client HTTP qui gère les appels avec authentification JWT."""
    def __init__(self, base_url: str, jwt_token: str, timer: Timer, verify_ssl: bool = True):
        self.base_url = base_url
        self.jwt_token = jwt_token
        self.timer = timer
        self.results = {}  # Pour stocker les résultats des appels précédents
        self.results_by_id = {}  # Pour stocker les résultats par ID personnalisé
        self.verify_ssl = verify_ssl
        self._shared_client = None  # Ajout: client HTTP partagé optionnel

        if not verify_ssl:
            # Supprimer les avertissements pour les requêtes non sécurisées
            warnings.filterwarnings("ignore", message="Unverified HTTPS request")
    async def make_request(self, endpoint: str, method: str = "GET", 
                       headers: Optional[Dict[str, str]] = None,
                       data: Optional[Dict[str, Any]] = None,
                       extract_key: Optional[str] = None,
                       extract_id: Optional[str] = None,
                       extract: Optional[List[Dict[str, str]]] = None,
                       thread_id: int = 0,
                       endpoint_id: Optional[str] = None,
                       file_path: Optional[str] = None,
                       form_name: str = "file",
                       current_step: int = 0,
                       total_steps: int = 0) -> Tuple[Dict[str, Any], bool]:
        """
        Fait une requête HTTP et chronomètre le temps de réponse.
        Retourne (données_réponse, continuer_séquence).
        """
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        response_data = {"error": "Une erreur inconnue s'est produite"}
        status_code = 0
        files = None

        # Afficher la progression
        if total_steps > 0:
            print(f"Thread {thread_id}: Étape {current_step}/{total_steps} - {endpoint_id or endpoint}")

        error_exc_type = None
        response = None  # Fix: Ensure 'response' is always defined
        error_url = url  # Ensure error_url is always defined
        try:
            # Préparer les en-têtes avec le jeton JWT
            request_headers = self._prepare_headers(headers)

            # Utiliser le client partagé si disponible, sinon créer un nouveau client
            if self._shared_client is not None:
                response, status_code = await self._execute_request(
                    self._shared_client, url, method, request_headers, data, file_path, form_name
                )
            else:
                async with httpx.AsyncClient(verify=self.verify_ssl) as client:
                    response, status_code = await self._execute_request(
                        client, url, method, request_headers, data, file_path, form_name
                    )

            # Vérifier s'il y a des erreurs d'authentification
            if self._is_auth_error(status_code):
                return {"error": f"Authentification refusée (HTTP {status_code})"}, False

            # Traiter la réponse et extraire les données
            response_data = self._process_response(
                response, status_code, endpoint, extract, extract_key,
                extract_id, endpoint_id
            )
        except Exception as e:
            # Gérer les différents types d'erreurs
            result = self._handle_request_exception(e, endpoint, return_exc_type=True)
            if len(result) == 3:
                response_data, status_code, error_exc_type = result
            else:
                response_data, status_code = result
                error_exc_type = None
            # error_url is already set above
        else:
            # error_url is already set above
            error_exc_type = None
        finally:
            # Fermer les ressources de fichier si nécessaires
            self._close_file_resources(files)
            self._close_file_resources(files)

            # Enregistrer les métriques de temps d'exécution
            self._record_timing_result(
                endpoint, thread_id, start_time, time.time(),
                status_code, endpoint_id
            )

            if (status_code >= 400 or status_code < 0):
                response_content = None
                if 'response' in locals() and response is not None:
                    try:
                        response_content = response.text
                    except Exception:
                        response_content = str(response)
                else:
                    response_content = str(response_data)
                # Ajout : passer le type d'exception au log d'erreur si status_code < 0
                await self.timer.log_error_response(
                    endpoint, thread_id, status_code, response_content, endpoint_id,
                    url=error_url,
                    exception_message=error_exc_type if error_exc_type is not None else ""
                )
        return response_data, True  # Par défaut, continuer la séquence

    def _prepare_headers(self, custom_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Prépare les en-têtes de la requête avec le jeton JWT."""
        headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}
        if custom_headers:
            headers.update(custom_headers)
        return headers

    async def _execute_request(
        self, client: httpx.AsyncClient, url: str, method: str,
        headers: Dict[str, str], data: Optional[Dict[str, Any]],
        file_path: Optional[str], form_name: str
    ) -> Tuple[httpx.Response, int]:
        """Exécute la requête HTTP selon la méthode spécifiée."""
        response = None

        if file_path:
            response = await self._upload_file(client, url, headers, file_path, form_name)
        elif method.upper() == "GET":
            response = await client.get(url, headers=headers, params=data, timeout=30.0)
        elif method.upper() == "POST":
            response = await client.post(url, headers=headers, json=data, timeout=30.0)
        elif method.upper() == "PUT":
            response = await client.put(url, headers=headers, json=data, timeout=30.0)
        elif method.upper() == "DELETE":
            response = await client.delete(url, headers=headers, json=data, timeout=30.0)
        else:
            raise ValueError(f"Méthode HTTP non supportée: {method}")

        return response, response.status_code

    async def _upload_file(
        self, client: httpx.AsyncClient, url: str, headers: Dict[str, str],
        file_path: str, form_name: str
    ) -> httpx.Response:
        """Téléverse un fichier vers le serveur."""
        # Vérifie si le fichier existe
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Le fichier '{file_path}' n'existe pas")

        # Détermine le type MIME du fichier
        file_type, _ = mimetypes.guess_type(file_path)
        if not file_type:
            file_type = 'application/octet-stream'

        # Lire le contenu du fichier de façon asynchrone
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()

        # Créer un form multipart avec le contenu du fichier
        files = {form_name: (os.path.basename(file_path), file_content, file_type)}

        print(f"Téléversement du fichier: {file_path} vers {url}")
        return await client.post(url, headers=headers, files=files, timeout=60.0)

    def _is_auth_error(self, status_code: int) -> bool:
        """Vérifie si le code de statut indique une erreur d'authentification."""
        return status_code in (401, 403)

    def _process_response(
        self, response: httpx.Response, status_code: int, endpoint: str,
        extract: Optional[List[Dict[str, str]]], extract_key: Optional[str],
        extract_id: Optional[str], endpoint_id: Optional[str]
    ) -> Dict[str, Any]:
        """Traite la réponse HTTP et extrait les données si nécessaire."""
        # Gestion des erreurs serveur
        if 500 <= status_code < 600:
            return self._handle_server_error(response, status_code)

        # Traitement de la réponse
        try:
            response_data = response.json()
        except Exception as e:
            return {"text": response.text, "error_parsing": str(e)}

        # Extraction des valeurs si nécessaire
        if status_code < 400:
            self._extract_values(response_data, endpoint, extract, extract_key, extract_id, endpoint_id)

        return response_data

    def _handle_server_error(self, response: httpx.Response, status_code: int) -> Dict[str, Any]:
        """Gère les erreurs serveur (5xx)."""
        error_message = "Erreur interne du serveur"
        try:
            # Essayer de récupérer le message d'erreur au format JSON
            error_data = response.json()
            if isinstance(error_data, dict):
                if "message" in error_data:
                    error_message = error_data["message"]
                elif "error" in error_data:
                    error_message = error_data["error"]
                elif "detail" in error_data:
                    error_message = error_data["detail"]
        except Exception:
            # Si le contenu n'est pas du JSON valide, récupérer le texte brut
            try:
                error_message = response.text[:200]  # Limiter à 200 caractères
            except Exception:
                pass  # Garder le message par défaut si tout échoue

        print(f"Erreur serveur (HTTP {status_code}): {error_message}")
        return {"error": error_message, "status_code": status_code}

    def _extract_values(
        self, response_data: Dict[str, Any], endpoint: str,
        extract: Optional[List[Dict[str, str]]], extract_key: Optional[str],
        extract_id: Optional[str], endpoint_id: Optional[str]
    ) -> None:
        """Extrait les valeurs de la réponse selon les configurations données."""
        # Traitement du nouveau format d'extraction (tableau d'objets)
        if extract:
            self._extract_values_from_list(response_data, extract, endpoint)

        # Ancien format d'extraction (pour compatibilité)
        elif extract_key:
            self._extract_single_value(response_data, extract_key, endpoint, endpoint_id, extract_id)

    def _extract_values_from_list(
        self, response_data: Dict[str, Any], extract_list: List[Dict[str, str]], endpoint: str
    ) -> None:
        """Extrait plusieurs valeurs à partir d'une liste de configurations."""
        for extraction in extract_list:
            if "key" in extraction and "valueFrom" in extraction:
                key_id = extraction["key"]
                value_key = extraction["valueFrom"]

                try:
                    if value_key in response_data:
                        extracted_value = response_data[value_key]
                        self.results_by_id[key_id] = extracted_value
                    else:
                        print(f"Avertissement: Clé '{value_key}' non trouvée dans la réponse de {endpoint}")
                except Exception as e:
                    print(f"Erreur lors de l'extraction de la clé '{value_key}': {str(e)}")

    def _extract_single_value(
        self, response_data: Dict[str, Any], extract_key: str,
        endpoint: str, endpoint_id: Optional[str], extract_id: Optional[str]
    ) -> None:
        """Extrait une seule valeur de la réponse."""
        try:
            if extract_key in response_data:
                # Stocke le résultat pour référence future (par URL et par ID)
                extracted_value = response_data[extract_key]
                self.results[endpoint] = extracted_value

                # Si un ID personnalisé est fourni, stocke également le résultat par ID
                if endpoint_id:
                    self.results_by_id[endpoint_id] = extracted_value
                    print(f"Valeur extraite de {endpoint} (ID: {endpoint_id}): {extract_key}={extracted_value}")

                # Si un ID spécifique pour la valeur extraite est fourni, l'utiliser aussi
                if extract_id:
                    self.results_by_id[extract_id] = extracted_value
                    print(f"Valeur extraite associée à l'ID personnalisé: {extract_id}={extracted_value}")

                if not endpoint_id and not extract_id:
                    print(f"Valeur extraite de {endpoint}: {extract_key}={extracted_value}")
            else:
                print(f"Avertissement: Clé '{extract_key}' non trouvée dans la réponse de {endpoint}")
        except Exception as e:
            print(f"Erreur lors de l'extraction de la clé '{extract_key}': {str(e)}")

    def _handle_request_exception(self, exception: Exception, endpoint: str, return_exc_type: bool = False) -> Union[Tuple[Dict[str, Any], int], Tuple[Dict[str, Any], int, str]]:
        """Gère les exceptions survenues pendant la requête."""
        if isinstance(exception, FileNotFoundError):
            status_code = -3
            exc_type = type(exception).__name__
            response_data = {"error": str(exception), "type": exc_type}
            print(f"Erreur de fichier: {str(exception)}")
        elif isinstance(exception, httpx.RequestError):
            status_code = -1
            exc_type = type(exception).__name__
            response_data = {"error": f"Erreur de requête HTTP: {str(exception)}", "type": exc_type}
            print(f"Erreur lors de l'appel à {endpoint}: {str(exception)}")
        else:
            status_code = -2
            exc_type = type(exception).__name__
            response_data = {"error": f"Exception: {str(exception)}", type: exc_type}
            print(f"Exception lors de l'appel à {endpoint}: {str(exception)}")
            traceback.print_exc()

        if return_exc_type:
            return response_data, status_code, exc_type
        return response_data, status_code

    def _close_file_resources(self, files: Optional[Dict]) -> None:
        """Ferme les ressources de fichiers ouvertes."""
        if files:
            for f in files.values():
                if hasattr(f[1], 'close'):
                    f[1].close()

    def _record_timing_result(
        self, endpoint: str, thread_id: int, start_time: float,
        end_time: float, status_code: int, endpoint_id: Optional[str]
    ) -> None:
        """Enregistre les résultats de timing pour la requête."""
        result = TimingResult(
            endpoint=endpoint,
            thread_id=thread_id,
            start_time=start_time,
            end_time=end_time,
            status_code=status_code,
            endpoint_id=endpoint_id
        )
        self.timer.add_result(result)
        print(result)

class TestSequence:
    """Exécute une séquence de tests HTTP."""
    def __init__(self, config: Dict[str, Any], timer: Timer, semaphore: Optional[asyncio.Semaphore] = None):
        self.config = config
        self.timer = timer
        self.semaphore = semaphore
        # Map pour retrouver les IDs des endpoints par URL
        self.url_to_id_map = {}

        # Construire la map URL -> ID pour une recherche facile
        for endpoint in config.get("endpoints", []):
            if "id" in endpoint and "url" in endpoint:
                self.url_to_id_map[endpoint["url"]] = endpoint["id"]

    def _create_format_dict(self, client: HttpClient) -> Dict[str, Any]:
        """
        Crée un dictionnaire de formatage avec toutes les valeurs extraites disponibles.
        Ce dictionnaire pourra être utilisé pour formater des chaînes avec la syntaxe {variable}.
        """
        format_dict = {}
        
        # Ajouter les valeurs extraites par URL
        for url, val in client.results.items():
            # Essayer de récupérer l'ID associé à l'URL pour un formatage plus intuitif
            if url in self.url_to_id_map:
                id_key = self.url_to_id_map[url].split('-')[-1]
                format_dict[id_key] = val
            # Aussi ajouter avec le chemin de l'URL (compatibilité)
            path = url.split('/')[-1]
            format_dict[path] = val

        # Ajouter aussi les valeurs par ID
        for id_key, val in client.results_by_id.items():
            # Ajouter l'ID complet sans modification
            format_dict[id_key] = val
            # Aussi ajouter la partie après le dernier tiret pour compatibilité
            id_part = id_key.split('-')[-1]
            format_dict[id_part] = val

        # Extraire les clés session_id de façon dynamique
        # Chercher session_id dans tous les résultats disponibles
        for session_key, session_value in client.results_by_id.items():
            if "session" in session_key.lower():
                format_dict["session_id"] = session_value
                break
                
        return format_dict

    def _format_string_safely(self, template: str, format_dict: Dict[str, Any], thread_id: int, context: str = "") -> Optional[str]:
        """
        Formate une chaîne en toute sécurité, en vérifiant les variables manquantes.
        Retourne la chaîne formatée ou None si des variables sont manquantes.
        
        Args:
            template: Chaîne à formater avec des variables entre accolades {var}
            format_dict: Dictionnaire contenant les valeurs à utiliser pour le formatage
            thread_id: ID du thread actuel pour les messages d'erreur
            context: Contexte pour les messages d'erreur (ex: "ID", "paramètre", "données")
            
        Returns:
            La chaîne formatée ou None si des variables sont manquantes
        """
        if not isinstance(template, str) or '{' not in template:
            return template
            
        # Vérifier si des variables requises sont manquantes
        missing_vars = self._find_missing_variables(template, format_dict)
        if missing_vars:
            print(f"Erreur critique: Variables requises manquantes dans {context}: {missing_vars}")
            print(f"Variables disponibles: {list(format_dict.keys())}")
            print(f"Séquence terminée pour le thread {thread_id}")
            return None

        try:
            return template.format(**format_dict)
        except KeyError as e:
            print(f"Erreur critique: Variable requise manquante lors du formatage {context}: {e}")
            print(f"Variables disponibles: {list(format_dict.keys())}")
            print(f"Séquence terminée pour le thread {thread_id}")
            return None

    def _find_missing_variables(self, template_string: str, available_vars: Dict[str, Any]) -> List[str]:
        """
        Trouve les variables manquantes dans une chaîne de modèle.
        Retourne une liste des noms de variables manquantes.
        """
        if not isinstance(template_string, str) or '{' not in template_string:
            return []

        # Extraire toutes les variables requises de la chaîne de modèle
        # Motif pour capturer les noms de variables entre accolades: {variable_name}
        var_pattern = r'\{([^{}]+)\}'
        required_vars = re.findall(var_pattern, template_string)

        # Vérifier quelles variables sont manquantes
        missing = [var for var in required_vars if var not in available_vars]

        # Afficher plus d'informations en cas de variables manquantes
        if missing:
            print(f"Variables requises: {required_vars}")
            print(f"Variables disponibles: {list(available_vars.keys())}")

        return missing

    async def run_sequence(self, thread_id: int = 0):
        """Exécute une séquence complète d'appels pour un thread avec client HTTP réutilisé et gestion de la concurrence."""
        try:
            verify_ssl = self.config.get("verify_ssl", True)
            max_concurrent = self.config.get("max_concurrent_per_thread", None)
            semaphore = asyncio.Semaphore(max_concurrent) if max_concurrent else None

            async with httpx.AsyncClient(verify=verify_ssl) as shared_client:
                client = HttpClient(
                    self.config["base_url"],
                    self.config["jwt_token"],
                    self.timer,
                    verify_ssl=verify_ssl
                )
                # Inject the shared client for reuse (if you want to refactor HttpClient to accept it)
                client._shared_client = shared_client

                total_steps = len(self.config["endpoints"])
                print(f"Thread {thread_id}: Démarrage - {total_steps} étapes à exécuter")

                for step, endpoint_config in enumerate(self.config["endpoints"], 1):
                    if semaphore:
                        async with semaphore:
                            await self._process_endpoint(client, endpoint_config, thread_id, step, total_steps)
                    else:
                        await self._process_endpoint(client, endpoint_config, thread_id, step, total_steps)

                print(f"Thread {thread_id}: Terminé - Toutes les {total_steps} étapes ont été exécutées")

        except Exception as e:
            print(f"Erreur catastrophique dans le thread {thread_id}: {str(e)}")
            traceback.print_exc()

    async def _process_endpoint(self, client: HttpClient, endpoint_config: Dict[str, Any], thread_id: int,
                               current_step: int, total_steps: int) -> None:
        """Traite un endpoint individuel."""
        try:
            # Préparer l'URL et l'ID de l'endpoint
            endpoint_url = endpoint_config["url"]
            endpoint_id = endpoint_config.get("id")
            endpoint_data = endpoint_config.get("data")

            # Traiter les dépendances si nécessaire
            if "depends_on" in endpoint_config and endpoint_config["depends_on"]:
                # Créer le dictionnaire de formatage avec toutes les valeurs extraites
                format_dict = self._create_format_dict(client)

                # Appliquer les formatages aux différents éléments de la requête
                result = self._prepare_request_with_dependencies(
                    endpoint_config, format_dict, thread_id, endpoint_url, endpoint_id
                )

                if result is None:
                    return  # Arrêter le traitement si une erreur critique survient

                endpoint_url, endpoint_id, endpoint_data = result

            # Préparer le chemin du fichier si nécessaire
            file_path = self._prepare_file_path(endpoint_config)
            form_name = endpoint_config.get("form_name", "file")

            # Exécuter la requête HTTP
            await self._execute_request(
                client, endpoint_url, endpoint_config, endpoint_data,
                endpoint_id, thread_id, file_path, form_name,
                current_step, total_steps
            )

        except Exception as e:
            print(f"Erreur dans la séquence (Thread {thread_id}, étape {current_step}/{total_steps}, endpoint {endpoint_config.get('url')}): {str(e)}")
            traceback.print_exc()
            # Continuer avec le prochain endpoint malgré l'erreur

    def _prepare_request_with_dependencies(
        self, endpoint_config: Dict[str, Any], format_dict: Dict[str, Any],
        thread_id: int, endpoint_url: str, endpoint_id: Optional[str]
    ) -> Optional[Tuple[str, Optional[str], Optional[Dict[str, Any]]]]:
        """
        Prépare la requête en appliquant les dépendances.
        Retourne un tuple (url, id, data) ou None en cas d'erreur.
        """
        # Formater l'ID de l'endpoint si nécessaire
        if endpoint_id and '{' in endpoint_id:
            formatted_id = self._format_string_safely(endpoint_id, format_dict, thread_id, "l'ID de l'endpoint")
            if formatted_id is None:
                return None
            endpoint_id = formatted_id

        # Formater l'URL si nécessaire
        if '{' in endpoint_url:
            formatted_url = self._format_string_safely(endpoint_url, format_dict, thread_id, "l'URL de l'endpoint")
            if formatted_url is None:
                return None
            endpoint_url = formatted_url

        # Traiter les paramètres d'URL
        url_result = self._process_url_params(endpoint_url, endpoint_config, format_dict, thread_id)
        if url_result is None:
            return None
        endpoint_url = url_result

        # Préparer les données avec les dépendances
        endpoint_data = self._process_request_data(endpoint_config, format_dict, thread_id)
        if endpoint_data is False:  # False indique une erreur (None est une valeur valide pour data)
            return None

        return endpoint_url, endpoint_id, endpoint_data if isinstance(endpoint_data, dict) or endpoint_data is None else None

    def _process_url_params(
        self, endpoint_url: str, endpoint_config: Dict[str, Any],
        format_dict: Dict[str, Any], thread_id: int
    ) -> Optional[str]:
        """
        Traite les paramètres d'URL et les ajoute à l'URL.
        Retourne l'URL mise à jour ou None en cas d'erreur.
        """
        params = endpoint_config.get("params", {})
        if not params:
            return endpoint_url

        # Copie pour éviter de modifier l'original
        params_copy = params.copy()

        # Remplace les modèles dans les paramètres
        for param_key, param_value in params_copy.items():
            if isinstance(param_value, str) and '{' in param_value:
                formatted_value = self._format_string_safely(
                    param_value, format_dict, thread_id, f"paramètre {param_key}"
                )
                if formatted_value is None:
                    return None
                params_copy[param_key] = formatted_value

        # Ajoute les paramètres à l'URL
        param_strings = [f"{key}={value}" for key, value in params_copy.items()]

        if param_strings:
            # Vérifie si l'URL contient déjà des paramètres
            separator = '&' if '?' in endpoint_url else '?'
            endpoint_url += separator + '&'.join(param_strings)

        return endpoint_url

    def _process_request_data(
        self, endpoint_config: Dict[str, Any], format_dict: Dict[str, Any], thread_id: int
    ) -> Union[Dict[str, Any], None, bool]:
        """
        Traite les données de la requête.
        Retourne les données formatées, None s'il n'y a pas de données, ou False en cas d'erreur.
        """
        if not endpoint_config.get("data"):
            return None

        # Copie les données pour éviter de modifier l'original
        endpoint_data = endpoint_config.get("data", {}).copy()

        # Copie du dictionnaire pour éviter de modifier l'original pendant l'itération
        endpoint_data_copy = endpoint_data.copy()

        # Remplace les modèles dans les données
        for data_key, data_value in endpoint_data_copy.items():
            if isinstance(data_value, str) and '{' in data_value:
                formatted_value = self._format_string_safely(
                    data_value, format_dict, thread_id, f"donnée {data_key}"
                )
                if formatted_value is None:
                    return False
                endpoint_data[data_key] = formatted_value

        return endpoint_data

    def _prepare_file_path(self, endpoint_config: Dict[str, Any]) -> Optional[str]:
        """Prépare le chemin du fichier à télécharger si nécessaire."""
        if "file" not in endpoint_config:
            return None

        # Si un chemin de fichier relatif est fourni, le résoudre par rapport au dossier "upload"
        file_path = endpoint_config["file"]
        if not os.path.isabs(file_path):
            file_path = os.path.join("upload", file_path)

        return file_path

    async def _execute_request(
        self, client: HttpClient, endpoint_url: str, endpoint_config: Dict[str, Any],
        endpoint_data: Optional[Dict[str, Any]], endpoint_id: Optional[str],
        thread_id: int, file_path: Optional[str], form_name: str,
        current_step: int, total_steps: int
    ) -> None:
        """Exécute la requête HTTP et gère la réponse."""
        # Fait l'appel HTTP
        _response_data, continue_sequence = await client.make_request(
            endpoint=endpoint_url,
            method=endpoint_config.get("method", "GET"),
            headers=endpoint_config.get("headers"),
            data=endpoint_data,
            extract=endpoint_config.get("extract"),
            thread_id=thread_id,
            endpoint_id=endpoint_id,
            file_path=file_path,
            form_name=form_name,
            current_step=current_step,
            total_steps=total_steps
        )

        # Si on doit terminer la séquence (ex: erreur d'authentification)
        if not continue_sequence:
            print(f"Thread {thread_id}: Séquence terminée prématurément à l'étape {current_step}/{total_steps}.")
            raise RuntimeError("Séquence terminée prématurément")

def decode_jwt_payload(jwt_token: str) -> Dict[str, Any]:
    """Décode la partie payload d'un jeton JWT sans vérifier la signature."""
    try:
        # Enlever le préfixe "Bearer " si présent
        if jwt_token.startswith("Bearer "):
            jwt_token = jwt_token[7:]

        # Un JWT est composé de 3 parties séparées par des points: header.payload.signature
        parts = jwt_token.split('.')
        if len(parts) != 3:
            raise ValueError("Format JWT invalide")

        # Décode la partie payload (2ème partie)
        payload_base64 = parts[1]
        # Ajuster la longueur pour qu'elle soit un multiple de 4
        payload_base64 += '=' * (-len(payload_base64) % 4)
        # Remplacer les caractères URL-safe par des caractères base64 standard
        payload_base64 = payload_base64.replace('-', '+').replace('_', '/')

        # Décoder la chaîne base64
        payload_json = base64.b64decode(payload_base64)
        payload = json.loads(payload_json)

        return payload
    except Exception as e:
        raise ValueError(f"Erreur lors du décodage du JWT: {str(e)}")

async def validate_jwt(base_url: str, jwt_token: str, verify_ssl: bool = True) -> Tuple[bool, str]:
    """
    Valide la validité d'un jeton JWT.
    Retourne un tuple (est_valide, message).
    """
    try:
        # 1. Vérifier la structure et la date d'expiration
        payload = decode_jwt_payload(jwt_token)

        # Vérifier si le jeton a une date d'expiration
        if "exp" in payload:
            exp_timestamp = payload["exp"]
            exp_datetime = datetime.datetime.fromtimestamp(exp_timestamp)
            now = datetime.datetime.now()

            if now > exp_datetime:
                return False, f"Le jeton JWT a expiré le {exp_datetime.strftime('%Y-%m-%d %H:%M:%S')}"

            # Avertir si le jeton expire bientôt (dans moins d'une heure)
            time_left = exp_datetime - now
            if time_left.total_seconds() < 3600:  # Moins d'une heure
                minutes_left = int(time_left.total_seconds() / 60)
                return True, f"Attention: Le jeton JWT expire dans {minutes_left} minutes (à {exp_datetime.strftime('%H:%M:%S')})"

        # 2. Faire un appel léger à l'API pour vérifier que le serveur accepte toujours le jeton
        # Cette étape est optionnelle car certaines APIs n'ont pas d'endpoint "léger" pour tester l'authentification
        try:
            async with httpx.AsyncClient(verify=verify_ssl) as client:
                # Utiliser un endpoint qui ne nécessite pas beaucoup de traitement
                # On suppose que l'endpoint /health ou /ping existe
                test_url = f"{base_url}/health"
                headers = {"X-CGPT-AUTHORIZATION": f"Bearer {jwt_token}"}

                response = await client.get(test_url, headers=headers, timeout=5.0)

                if response.status_code in (401, 403):
                    return False, f"Le serveur a rejeté le jeton JWT (HTTP {response.status_code})"
        except Exception as e:
            # Si l'appel de test échoue, on se fie uniquement à la vérification de la date d'expiration
            print(f"Avertissement: Impossible de vérifier le jeton sur le serveur: {str(e)}")

        return True, "Le jeton JWT est valide"
    except Exception as e:
        return False, f"Erreur lors de la validation du JWT: {str(e)}"

async def run_tests(config_path: str, skip_jwt_validation: bool = False):
    """Fonction principale pour exécuter les tests de charge."""
    try:
        global_config_path, context_config_path = get_config_paths(config_path)

        # Ajout : afficher le fichier de contexte utilisé dans le rapport
        print(f"Fichier de contexte utilisé : {context_config_path}")

        # ...chargement des configs et fusion...
        async with aiofiles.open(global_config_path, 'r') as gf:
            global_content = await gf.read()
            global_config = json.loads(global_content)
        if context_config_path is None:
            raise ValueError("Le chemin du fichier de contexte ne doit pas être None.")
        async with aiofiles.open(context_config_path, 'r') as cf:
            context_content = await cf.read()
            context_config = json.loads(context_content)
        config = global_config.copy()
        config.update(context_config)
        if "endpoints" in context_config:
            config["endpoints"] = context_config["endpoints"]

        # Ajout : stocker le chemin du contexte dans la config pour le rapport
        config["_context_file"] = context_config_path

        # Valider le jeton JWT avant de démarrer les tests
        if not skip_jwt_validation:
            print("Validation du jeton JWT...")
            jwt_valid, jwt_message = await validate_jwt(
                config["base_url"],
                config["jwt_token"],
                config.get("verify_ssl", True)
            )

            print(jwt_message)

            if not jwt_valid:
                print("Les tests ne peuvent pas être exécutés avec un jeton JWT invalide, voir fichier config.global.json.")
                return

        timer = Timer()
        timer.start_global()

        # Configurer les informations sur les threads et tâches
        num_threads = config.get("num_threads", 1)
        num_tasks_per_thread = len(config.get("endpoints", []))
        timer.set_thread_and_task_info(num_threads, num_tasks_per_thread)

        # Crée et exécute les threads en parallèle
        tasks = []
        for i in range(num_threads):
            test_sequence = TestSequence(config, timer)
            task = asyncio.create_task(test_sequence.run_sequence(thread_id=i))
            tasks.append(task)

        # Attend que tous les threads terminent
        await asyncio.gather(*tasks)

        timer.end_global()
        await timer.print_summary(context_file=config.get("_context_file"), base_url=config.get("base_url"))
    except json.JSONDecodeError as e:
        print(f"Erreur: Le fichier de configuration n'est pas un JSON valide: {str(e)}")
    except FileNotFoundError:
        print(f"Erreur: Le fichier de configuration '{config_path}' est introuvable")
    except Exception as e:
        print(f"Erreur lors de l'exécution des tests: {str(e)}")
        traceback.print_exc()

def get_config_paths(config_path: str) -> Tuple[str, Optional[str]]:
    """
    Détermine le chemin du fichier global et du contexte à partir du paramètre -c.
    Si le paramètre contient un ",", on considère le premier comme global et le second comme contexte.
    Sinon, on utilise config.global.json + le paramètre comme contexte.
    """
    if "," in config_path:
        parts = [p.strip() for p in config_path.split(",")]
        if len(parts) == 2:
            return parts[0], parts[1]
    # Par défaut, global = config.global.json, contexte = config_path
    return "config.global.json", config_path

def main():
    parser = argparse.ArgumentParser(description="Application de test de charge HTTP")
    parser.add_argument("-c", "--config", required=True, help="Chemin vers le fichier de configuration JSON")
    parser.add_argument("--skip-jwt-validation", action="store_true", help="Ignorer la validation du jeton JWT")

    args = parser.parse_args()
    
    try:
        asyncio.run(run_tests(args.config, args.skip_jwt_validation))
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    main()