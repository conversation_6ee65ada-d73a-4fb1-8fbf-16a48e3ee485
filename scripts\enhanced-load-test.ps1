# Enhanced Load Testing PowerShell Script
# =======================================
# 
# Provides a PowerShell interface for the enhanced Locust load testing framework.
# Supports various test types, configuration management, and result analysis.

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("smoke", "load", "stress", "spike", "endurance", "baseline")]
    [string]$TestType = "smoke",
    
    [Parameter(Mandatory=$false)]
    [string]$Context = "andoc",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "staging", "production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [int]$Users = 10,
    
    [Parameter(Mandatory=$false)]
    [float]$SpawnRate = 1.0,
    
    [Parameter(Mandatory=$false)]
    [string]$Duration = "5m",
    
    [Parameter(Mandatory=$false)]
    [switch]$TestSuite,
    
    [Parameter(Mandatory=$false)]
    [string]$Contexts = "andoc,talperftraitement",
    
    [Parameter(Mandatory=$false)]
    [switch]$Monitor,
    
    [Parameter(Mandatory=$false)]
    [switch]$Distributed,
    
    [Parameter(Mandatory=$false)]
    [int]$Workers = 2,
    
    [Parameter(Mandatory=$false)]
    [switch]$WebUI,
    
    [Parameter(Mandatory=$false)]
    [string]$ReportDir = "reports",
    
    [Parameter(Mandatory=$false)]
    [switch]$ExportMetrics,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose,
    
    [Parameter(Mandatory=$false)]
    [switch]$Help
)

# Display help information
if ($Help) {
    Write-Host @"
Enhanced Load Testing PowerShell Script
======================================

USAGE:
    .\enhanced-load-test.ps1 [OPTIONS]

OPTIONS:
    -TestType <type>        Type of test to run (smoke, load, stress, spike, endurance, baseline)
    -Context <context>      Context for the test (andoc, talperftraitement)
    -Environment <env>      Environment configuration (dev, staging, production)
    -Users <number>         Number of concurrent users
    -SpawnRate <rate>       User spawn rate (users per second)
    -Duration <time>        Test duration (e.g., 5m, 30s, 1h)
    -TestSuite             Run comprehensive test suite
    -Contexts <list>        Comma-separated contexts for test suite
    -Monitor               Enable real-time monitoring
    -Distributed           Run distributed test
    -Workers <number>       Number of worker processes for distributed testing
    -WebUI                 Enable Locust web UI
    -ReportDir <path>       Directory for test reports
    -ExportMetrics         Export detailed metrics to JSON
    -Verbose               Enable verbose logging
    -Help                  Show this help message

EXAMPLES:
    # Run smoke test for andoc context
    .\enhanced-load-test.ps1 -TestType smoke -Context andoc
    
    # Run load test with 100 users for 10 minutes
    .\enhanced-load-test.ps1 -TestType load -Context andoc -Users 100 -Duration 10m
    
    # Run comprehensive test suite
    .\enhanced-load-test.ps1 -TestSuite -Contexts "andoc,talperftraitement"
    
    # Run distributed stress test with monitoring
    .\enhanced-load-test.ps1 -TestType stress -Context andoc -Distributed -Workers 3 -Monitor
    
    # Run with web UI for interactive testing
    .\enhanced-load-test.ps1 -TestType load -Context andoc -WebUI

"@
    exit 0
}

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $originalColor = $Host.UI.RawUI.ForegroundColor
    $Host.UI.RawUI.ForegroundColor = $Color
    Write-Host $Message
    $Host.UI.RawUI.ForegroundColor = $originalColor
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." "Cyan"
    
    # Check Python
    try {
        $pythonVersion = python --version 2>&1
        Write-ColorOutput "✅ Python: $pythonVersion" "Green"
    }
    catch {
        Write-ColorOutput "❌ Python not found. Please install Python 3.8+" "Red"
        exit 1
    }
    
    # Check if virtual environment exists
    if (Test-Path ".venv") {
        Write-ColorOutput "✅ Virtual environment found" "Green"
    }
    else {
        Write-ColorOutput "⚠️  Virtual environment not found. Creating..." "Yellow"
        python -m venv .venv
    }
    
    # Activate virtual environment
    $venvActivate = ".venv\Scripts\Activate.ps1"
    if (Test-Path $venvActivate) {
        Write-ColorOutput "🔄 Activating virtual environment..." "Cyan"
        & $venvActivate
    }
    
    # Check if locust is installed
    try {
        $locustVersion = locust --version 2>&1
        Write-ColorOutput "✅ Locust: $locustVersion" "Green"
    }
    catch {
        Write-ColorOutput "⚠️  Locust not found. Installing dependencies..." "Yellow"
        pip install -e .
    }
    
    # Check configuration files
    if (Test-Path "config.global.json") {
        Write-ColorOutput "✅ Global configuration found" "Green"
    }
    else {
        Write-ColorOutput "❌ config.global.json not found" "Red"
        exit 1
    }
    
    Write-ColorOutput "✅ Prerequisites check completed" "Green"
}

# Function to validate JWT token
function Test-JWTToken {
    Write-ColorOutput "🔐 Validating JWT token..." "Cyan"
    
    try {
        $config = Get-Content "config.global.json" | ConvertFrom-Json
        $token = $config.jwt_token
        
        if (-not $token) {
            Write-ColorOutput "❌ No JWT token found in configuration" "Red"
            exit 1
        }
        
        # Basic token validation (decode without verification)
        $tokenParts = $token.Split('.')
        if ($tokenParts.Length -ne 3) {
            Write-ColorOutput "❌ Invalid JWT token format" "Red"
            exit 1
        }
        
        # Decode payload (basic check)
        $payload = $tokenParts[1]
        # Add padding if needed
        while ($payload.Length % 4 -ne 0) {
            $payload += "="
        }
        
        try {
            $decodedBytes = [System.Convert]::FromBase64String($payload)
            $decodedText = [System.Text.Encoding]::UTF8.GetString($decodedBytes)
            $payloadObj = $decodedText | ConvertFrom-Json
            
            # Check expiration
            $exp = $payloadObj.exp
            $currentTime = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
            
            if ($exp -lt $currentTime) {
                Write-ColorOutput "❌ JWT token has expired" "Red"
                exit 1
            }
            
            $expiresIn = ($exp - $currentTime) / 3600
            Write-ColorOutput "✅ JWT token is valid (expires in $([math]::Round($expiresIn, 1)) hours)" "Green"
        }
        catch {
            Write-ColorOutput "⚠️  Could not validate token expiration, proceeding anyway..." "Yellow"
        }
    }
    catch {
        Write-ColorOutput "❌ Error validating JWT token: $_" "Red"
        exit 1
    }
}

# Function to create reports directory
function Initialize-ReportsDirectory {
    if (-not (Test-Path $ReportDir)) {
        New-Item -ItemType Directory -Path $ReportDir -Force | Out-Null
        Write-ColorOutput "📁 Created reports directory: $ReportDir" "Green"
    }
}

# Function to run the test
function Invoke-LoadTest {
    Write-ColorOutput "🚀 Starting load test execution..." "Cyan"
    
    # Build command arguments
    $args = @()
    
    if ($TestSuite) {
        $args += "--test-suite"
        $args += "--contexts", $Contexts
    }
    else {
        $args += "--test-type", $TestType
        $args += "--context", $Context
    }
    
    if ($Environment) {
        $args += "--environment", $Environment
    }
    
    if ($Users) {
        $args += "--users", $Users
    }
    
    if ($SpawnRate) {
        $args += "--spawn-rate", $SpawnRate
    }
    
    if ($Duration) {
        $args += "--duration", $Duration
    }
    
    if ($Monitor) {
        $args += "--monitor"
    }
    
    if ($Distributed) {
        $args += "--distributed"
        $args += "--workers", $Workers
    }
    
    if ($WebUI) {
        $args += "--web-ui"
    }
    
    if ($ReportDir) {
        $args += "--report-dir", $ReportDir
    }
    
    if ($ExportMetrics) {
        $args += "--export-metrics"
    }
    
    if ($Verbose) {
        $args += "--verbose"
    }
    
    # Execute the test
    $command = "python scripts/run_load_tests.py " + ($args -join " ")
    Write-ColorOutput "📋 Command: $command" "Gray"
    
    try {
        $startTime = Get-Date
        
        # Run the test
        & python scripts/run_load_tests.py @args
        $exitCode = $LASTEXITCODE
        
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        Write-ColorOutput "⏱️  Test completed in $($duration.TotalMinutes.ToString('F1')) minutes" "Cyan"
        
        if ($exitCode -eq 0) {
            Write-ColorOutput "✅ Load test completed successfully!" "Green"
        }
        else {
            Write-ColorOutput "❌ Load test failed with exit code: $exitCode" "Red"
        }
        
        return $exitCode
    }
    catch {
        Write-ColorOutput "❌ Error executing load test: $_" "Red"
        return 1
    }
}

# Function to display results
function Show-TestResults {
    Write-ColorOutput "📊 Checking for test results..." "Cyan"
    
    # Look for HTML reports
    $htmlReports = Get-ChildItem -Path $ReportDir -Filter "*.html" -ErrorAction SilentlyContinue
    if ($htmlReports) {
        Write-ColorOutput "📄 HTML Reports generated:" "Green"
        foreach ($report in $htmlReports) {
            Write-ColorOutput "   - $($report.FullName)" "Gray"
        }
        
        # Open the most recent report
        $latestReport = $htmlReports | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        Write-ColorOutput "🌐 Opening latest report: $($latestReport.Name)" "Cyan"
        Start-Process $latestReport.FullName
    }
    
    # Look for CSV reports
    $csvReports = Get-ChildItem -Path $ReportDir -Filter "*.csv" -ErrorAction SilentlyContinue
    if ($csvReports) {
        Write-ColorOutput "📊 CSV Reports generated:" "Green"
        foreach ($report in $csvReports) {
            Write-ColorOutput "   - $($report.FullName)" "Gray"
        }
    }
    
    # Look for JSON metrics
    $jsonMetrics = Get-ChildItem -Path $ReportDir -Filter "*.json" -ErrorAction SilentlyContinue
    if ($jsonMetrics) {
        Write-ColorOutput "📈 JSON Metrics exported:" "Green"
        foreach ($metric in $jsonMetrics) {
            Write-ColorOutput "   - $($metric.FullName)" "Gray"
        }
    }
}

# Main execution
try {
    Write-ColorOutput "🎯 Enhanced Load Testing Framework" "Magenta"
    Write-ColorOutput "=================================" "Magenta"
    
    # Check prerequisites
    Test-Prerequisites
    
    # Validate JWT token
    Test-JWTToken
    
    # Initialize reports directory
    Initialize-ReportsDirectory
    
    # Run the test
    $exitCode = Invoke-LoadTest
    
    # Show results
    Show-TestResults
    
    # Final status
    if ($exitCode -eq 0) {
        Write-ColorOutput "🎉 Load testing completed successfully!" "Green"
    }
    else {
        Write-ColorOutput "💥 Load testing failed. Check the logs for details." "Red"
    }
    
    exit $exitCode
}
catch {
    Write-ColorOutput "💥 Unexpected error: $_" "Red"
    exit 1
}
finally {
    Write-ColorOutput "🏁 Load testing session ended." "Cyan"
}
