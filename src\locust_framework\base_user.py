"""
Base User Classes for Enhanced Locust Load Testing
=================================================

Provides enhanced HttpUser classes with JWT authentication, session management,
and advanced request handling capabilities.
"""

import json
import random
import logging
from typing import Dict, Any, Optional, List
from locust import HttpUser, task, between
from locust.clients import HttpSession

from .utils import J<PERSON>TValidator, SessionManager, RequestTracker
from .config_manager import TestConfig

logger = logging.getLogger(__name__)


class BaseAPIUser(HttpUser):
    """
    Enhanced base user class with configuration management and utilities
    """
    abstract = True
    
    def __init__(self, environment):
        super().__init__(environment)
        self.config: Optional[TestConfig] = None
        self.request_tracker = RequestTracker()
        self.user_data = {}
        
    def on_start(self):
        """Initialize user - override in subclasses"""
        logger.info(f"User {self.user_id} starting")
        
    def on_stop(self):
        """Cleanup when user stops"""
        logger.info(f"User {self.user_id} stopping")
        
        # Log user statistics
        stats = self.request_tracker.get_statistics()
        if stats:
            logger.info(f"User {self.user_id} stats: {stats}")
    
    def make_request(self, method: str, url: str, name: Optional[str] = None,
                    catch_response: bool = True, **kwargs) -> Any:
        """
        Enhanced request method with tracking and error handling
        
        Args:
            method: HTTP method
            url: Request URL
            name: Custom name for request (for Locust stats)
            catch_response: Whether to catch response for custom handling
            **kwargs: Additional arguments for the request
            
        Returns:
            Response object or context manager
        """
        # Use custom name or URL for stats
        request_name = name or url
        
        # Make the request
        if method.upper() == "GET":
            response = self.client.get(url, name=request_name, 
                                     catch_response=catch_response, **kwargs)
        elif method.upper() == "POST":
            response = self.client.post(url, name=request_name, 
                                      catch_response=catch_response, **kwargs)
        elif method.upper() == "PUT":
            response = self.client.put(url, name=request_name, 
                                     catch_response=catch_response, **kwargs)
        elif method.upper() == "DELETE":
            response = self.client.delete(url, name=request_name, 
                                        catch_response=catch_response, **kwargs)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        # Track the request
        if hasattr(response, 'status_code'):
            self.request_tracker.record_request(
                method=method.upper(),
                url=url,
                response_time=response.elapsed.total_seconds() * 1000,
                status_code=response.status_code,
                error=None if response.status_code < 400 else f"HTTP_{response.status_code}"
            )
        
        return response
    
    def extract_data(self, response, extractions: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Extract data from response for use in subsequent requests
        
        Args:
            response: HTTP response object
            extractions: List of extraction rules [{"key": "user_id", "valueFrom": "id"}]
            
        Returns:
            Dict of extracted values
        """
        extracted = {}
        
        try:
            if response.status_code == 200:
                data = response.json()
                
                for extraction in extractions:
                    key = extraction.get("key")
                    value_from = extraction.get("valueFrom")
                    
                    if key and value_from and value_from in data:
                        extracted[key] = data[value_from]
                        logger.debug(f"Extracted {key}={data[value_from]}")
                
        except Exception as e:
            logger.error(f"Error extracting data from response: {e}")
        
        return extracted
    
    def substitute_variables(self, text: str, variables: Dict[str, Any]) -> str:
        """
        Substitute variables in text using {variable_name} format
        
        Args:
            text: Text with variable placeholders
            variables: Dictionary of variable values
            
        Returns:
            Text with variables substituted
        """
        result = text
        for key, value in variables.items():
            placeholder = f"{{{key}}}"
            if placeholder in result:
                result = result.replace(placeholder, str(value))
        return result


class AuthenticatedUser(BaseAPIUser):
    """
    User class with JWT authentication and session management
    """
    abstract = True
    
    def __init__(self, environment):
        super().__init__(environment)
        self.jwt_token: Optional[str] = None
        self.session_manager: Optional[SessionManager] = None
        self.auth_headers: Dict[str, str] = {}
        
    def on_start(self):
        """Initialize authenticated user with JWT and session"""
        super().on_start()
        
        # Get JWT token from environment or config
        self.jwt_token = getattr(self.environment, 'jwt_token', None)
        if not self.jwt_token:
            logger.error("No JWT token available for authenticated user")
            return
        
        # Validate token
        token_info = JWTValidator.get_token_info(self.jwt_token)
        if token_info['expired']:
            logger.error("JWT token is expired")
            return
        
        logger.info(f"User authenticated as: {token_info.get('name', 'Unknown')}")
        
        # Set up authentication headers
        self.auth_headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}
        
        # Initialize session manager
        base_url = getattr(self.environment, 'host', 'http://localhost')
        verify_ssl = getattr(self.environment, 'verify_ssl', True)
        
        self.session_manager = SessionManager(
            base_url=base_url,
            jwt_token=self.jwt_token,
            verify_ssl=verify_ssl
        )
        
        # Create session for default context
        self.create_user_session()
    
    def create_user_session(self, context: str = "andoc") -> bool:
        """Create a session for the user"""
        if not self.session_manager:
            return False
        
        # Use synchronous approach for Locust compatibility
        # Note: In a real implementation, you might want to use a sync HTTP client
        # or handle this differently to avoid blocking the event loop
        try:
            # For now, we'll simulate session creation
            # In production, replace with actual sync HTTP call
            self.session_manager.session_data.session_id = f"session_{random.randint(1000, 9999)}"
            self.session_manager.session_data.context_data["context"] = context
            logger.info(f"Session created for context {context}")
            return True
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return False
    
    def get_auth_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get headers with authentication and session info"""
        headers = self.auth_headers.copy()
        
        if self.session_manager:
            session_headers = self.session_manager.get_session_headers()
            headers.update(session_headers)
        
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def authenticated_request(self, method: str, url: str, 
                            additional_headers: Optional[Dict[str, str]] = None,
                            **kwargs) -> Any:
        """Make an authenticated request"""
        headers = self.get_auth_headers(additional_headers)
        
        # Merge with any headers in kwargs
        if 'headers' in kwargs:
            headers.update(kwargs['headers'])
        kwargs['headers'] = headers
        
        return self.make_request(method, url, **kwargs)


class ScenarioUser(AuthenticatedUser):
    """
    User class that executes predefined scenarios from configuration
    """
    
    def __init__(self, environment):
        super().__init__(environment)
        self.scenarios: List[Dict[str, Any]] = []
        self.extracted_variables: Dict[str, Any] = {}
        
    def on_start(self):
        """Initialize scenario user"""
        super().on_start()
        
        # Load scenarios from environment
        self.scenarios = getattr(self.environment, 'scenarios', [])
        if not self.scenarios:
            logger.warning("No scenarios configured for ScenarioUser")
    
    @task
    def execute_scenario(self):
        """Execute a random scenario"""
        if not self.scenarios:
            return
        
        scenario = random.choice(self.scenarios)
        self.run_scenario(scenario)
    
    def run_scenario(self, scenario: Dict[str, Any]):
        """
        Run a specific scenario
        
        Args:
            scenario: Scenario configuration with endpoints and flow
        """
        scenario_name = scenario.get("name", "unnamed_scenario")
        endpoints = scenario.get("endpoints", [])
        
        logger.debug(f"Running scenario: {scenario_name}")
        
        for endpoint_config in endpoints:
            self.execute_endpoint(endpoint_config)
    
    def execute_endpoint(self, endpoint_config: Dict[str, Any]):
        """Execute a single endpoint from scenario"""
        url = endpoint_config.get("url", "/")
        method = endpoint_config.get("method", "GET").upper()
        data = endpoint_config.get("data", {})
        params = endpoint_config.get("params", {})
        extractions = endpoint_config.get("extract", [])
        endpoint_id = endpoint_config.get("id", url)
        
        # Substitute variables in URL, data, and params
        url = self.substitute_variables(url, self.extracted_variables)
        
        if isinstance(data, dict):
            data = {k: self.substitute_variables(str(v), self.extracted_variables) 
                   for k, v in data.items()}
        
        if isinstance(params, dict):
            params = {k: self.substitute_variables(str(v), self.extracted_variables) 
                     for k, v in params.items()}
        
        # Make the request
        try:
            if method == "GET":
                response = self.authenticated_request("GET", url, params=params, name=endpoint_id)
            elif method == "POST":
                response = self.authenticated_request("POST", url, json=data, params=params, name=endpoint_id)
            elif method == "PUT":
                response = self.authenticated_request("PUT", url, json=data, params=params, name=endpoint_id)
            elif method == "DELETE":
                response = self.authenticated_request("DELETE", url, json=data, params=params, name=endpoint_id)
            else:
                logger.error(f"Unsupported method: {method}")
                return
            
            # Handle response
            if hasattr(response, 'status_code'):
                if response.status_code >= 400:
                    logger.warning(f"Request failed: {method} {url} -> {response.status_code}")
                else:
                    # Extract data if configured
                    if extractions:
                        extracted = self.extract_data(response, extractions)
                        self.extracted_variables.update(extracted)
                        
        except Exception as e:
            logger.error(f"Error executing endpoint {url}: {e}")
