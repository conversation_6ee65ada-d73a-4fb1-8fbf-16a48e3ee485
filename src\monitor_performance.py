import psutil
import asyncio
import json
from datetime import datetime

class PerformanceMonitor:
    async def monitor_during_test(self, duration_seconds: int):
        """Monitor system resources during load test"""
        metrics = []
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < duration_seconds:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            network = psutil.net_io_counters()
            
            metrics.append({
                "timestamp": datetime.now().isoformat(),
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "network_sent": network.bytes_sent,
                "network_recv": network.bytes_recv
            })
            
            await asyncio.sleep(5)
        
        return metrics