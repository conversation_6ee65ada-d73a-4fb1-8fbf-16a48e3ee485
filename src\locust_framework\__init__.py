"""
Enhanced Locust Framework for Load Testing
==========================================

This package provides a comprehensive framework for load testing using Locust
with advanced features like JWT authentication, session management, custom
statistics, and enhanced reporting.

Key Components:
- BaseUser: Enhanced HttpUser with JWT and session management
- ConfigManager: Centralized configuration management
- ReportManager: Advanced reporting and statistics
- TestOrchestrator: Test execution coordination
"""

from .base_user import BaseAPIUser, AuthenticatedUser
from .config_manager import ConfigManager
from .report_manager import ReportManager, CustomStats
from .test_orchestrator import TestOrchestrator
from .utils import JWTValidator, SessionManager

__version__ = "1.0.0"
__all__ = [
    "BaseAPIUser",
    "AuthenticatedUser", 
    "ConfigManager",
    "ReportManager",
    "CustomStats",
    "TestOrchestrator",
    "JWTValidator",
    "SessionManager"
]
