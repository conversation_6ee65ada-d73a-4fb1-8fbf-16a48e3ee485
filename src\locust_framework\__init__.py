"""
Enhanced Locust Framework for Load Testing
==========================================

This package provides a comprehensive framework for load testing using Locust
with advanced features like JWT authentication, session management, custom
statistics, and enhanced reporting.

Key Components:
- BaseUser: Enhanced HttpUser with JWT and session management
- ConfigManager: Centralized configuration management
- ReportManager: Advanced reporting and statistics
- TestOrchestrator: Test execution coordination
"""

from .base_user import BaseAPIUser, AuthenticatedUser, ScenarioUser
from .config_manager import ConfigManager
from .report_manager import ReportManager, CustomStats
from .test_orchestrator import TestOrchestrator, TestType
from .utils import JWTValidator, SessionManager

__version__ = "1.0.0"
__all__ = [
    "BaseAPIUser",
    "AuthenticatedUser",
    "ScenarioUser",
    "ConfigManager",
    "ReportManager",
    "CustomStats",
    "TestOrchestrator",
    "TestType",
    "JWTValidator",
    "SessionManager"
]
