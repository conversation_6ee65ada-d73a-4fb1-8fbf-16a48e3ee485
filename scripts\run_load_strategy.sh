#!/bin/bash

echo "🎯 Comprehensive Load Testing Strategy for 2000 Users"
echo "=================================================="

# 1. System Preparation
echo "📋 Step 1: System Preparation"
python -c "
import psutil
print(f'CPU Cores: {psutil.cpu_count()}')
print(f'RAM: {psutil.virtual_memory().total / (1024**3):.1f} GB')
print(f'Available RAM: {psutil.virtual_memory().available / (1024**3):.1f} GB')
"

# 2. Baseline Test (Single User)
echo -e "\n🔍 Step 2: Baseline Test (1 user)"
python scripts/baseline_test.py

# 3. Authentication Load Test
echo -e "\n🔐 Step 3: Authentication Load Test (500 users)"
python scripts/auth_load_test.py

# 4. Connection Pool Test
echo -e "\n🔗 Step 4: Connection Pool Test (10 users)"
python scripts/test_connection_limits.py

# 5. Database Load Test
echo -e "\n🗄️ Step 5: Database Load Test (100 connections)"
python scripts/database_load_test.py

# 6. Memory Leak Test
echo -e "\n🧠 Step 6: Memory Leak Test (100 users, 10 minutes)"
python scripts/memory_leak_test.py --users 100 --duration 600

# 7. Error Resilience Test
echo -e "\n🚨 Step 7: Error Resilience Test"
python scripts/error_resilience_test.py

# 8. Latency Analysis
echo -e "\n📊 Step 8: Latency Distribution Analysis (1000 samples)"
python scripts/latency_analysis_test.py

# 9. Gradual Ramp-Up Test
echo -e "\n📈 Step 9: Gradual Ramp-Up Test"
python scripts/robust_load_test.py config.global.json

# 10. Resource Saturation Test
echo -e "\n💾 Step 10: Resource Saturation Test"
python scripts/resource_saturation_test.py

# 11. Distributed Locust Test
echo -e "\n🌐 Step 11: Distributed Locust Test"
echo "Run this command in separate terminals:"
echo "Master: locust -f scripts/distributed_locust_test.py --master --host=https://mia-ta.lacaisse.com"
echo "Worker: locust -f scripts/distributed_locust_test.py --worker --master-host=localhost"

# 12. Stress Test (Beyond Capacity)
echo -e "\n💥 Step 12: Stress Test (3000 users)"
python scripts/stress_test.py --users 3000

# 13. Recovery Test
echo -e "\n🔄 Step 13: Recovery Test"
python scripts/recovery_test.py

# 14. Soak Test (Optional - Long Duration)
echo -e "\n🕐 Step 14: Soak Test (Optional - 4 hours)"
echo "To run soak test: python scripts/soak_test.py --hours 4 --users 200"

echo -e "\n✅ All tests completed! Check reports in ./reports/ directory"
