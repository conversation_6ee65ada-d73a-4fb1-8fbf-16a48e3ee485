#!/usr/bin/env python3
"""
Enhanced Load Test Orchestration Script
======================================

Orchestrates different types of load tests using the enhanced Locust framework.
Provides a unified interface for running various test scenarios with proper
configuration management and reporting.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from locust_framework import TestOrchestrator, TestType, ConfigManager
from locust_framework.real_time_monitor import RealTimeMonitor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main orchestration function"""
    parser = argparse.ArgumentParser(
        description="Enhanced Load Test Orchestration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run smoke test for andoc context
  python scripts/run_load_tests.py --test-type smoke --context andoc
  
  # Run full test suite for multiple contexts
  python scripts/run_load_tests.py --test-suite --contexts andoc,talperftraitement
  
  # Run stress test with custom configuration
  python scripts/run_load_tests.py --test-type stress --context andoc --users 500 --duration 10m
  
  # Run load test with real-time monitoring
  python scripts/run_load_tests.py --test-type load --context andoc --monitor
  
  # Run distributed test
  python scripts/run_load_tests.py --test-type load --context andoc --distributed --workers 3
        """
    )
    
    # Test configuration
    parser.add_argument(
        "--test-type", 
        type=str,
        choices=[t.value for t in TestType],
        help="Type of test to run"
    )
    
    parser.add_argument(
        "--test-suite",
        action="store_true",
        help="Run a comprehensive test suite"
    )
    
    parser.add_argument(
        "--context",
        type=str,
        help="Context for the test (e.g., andoc, talperftraitement)"
    )
    
    parser.add_argument(
        "--contexts",
        type=str,
        help="Comma-separated list of contexts for test suite"
    )
    
    parser.add_argument(
        "--environment",
        type=str,
        choices=["dev", "staging", "production"],
        help="Environment configuration to use"
    )
    
    # Test parameters
    parser.add_argument(
        "--users",
        type=int,
        help="Number of concurrent users"
    )
    
    parser.add_argument(
        "--spawn-rate",
        type=float,
        help="User spawn rate (users per second)"
    )
    
    parser.add_argument(
        "--duration",
        type=str,
        help="Test duration (e.g., 5m, 30s, 1h)"
    )
    
    # Advanced options
    parser.add_argument(
        "--monitor",
        action="store_true",
        help="Enable real-time monitoring"
    )
    
    parser.add_argument(
        "--distributed",
        action="store_true",
        help="Run distributed test"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="Number of worker processes for distributed testing"
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        default=True,
        help="Run in headless mode (default: True)"
    )
    
    parser.add_argument(
        "--web-ui",
        action="store_true",
        help="Enable Locust web UI"
    )
    
    # Output options
    parser.add_argument(
        "--report-dir",
        type=str,
        default="reports",
        help="Directory for test reports"
    )
    
    parser.add_argument(
        "--export-metrics",
        action="store_true",
        help="Export detailed metrics to JSON"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Configure logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate arguments
    if not args.test_suite and not args.test_type:
        parser.error("Either --test-type or --test-suite must be specified")
    
    if args.test_suite and not args.contexts:
        parser.error("--contexts must be specified when using --test-suite")
    
    if args.test_type and not args.context:
        parser.error("--context must be specified when using --test-type")
    
    try:
        # Initialize orchestrator
        orchestrator = TestOrchestrator()
        
        # Setup real-time monitoring if requested
        monitor = None
        if args.monitor:
            monitor = RealTimeMonitor()
            monitor.start_monitoring()
            logger.info("Real-time monitoring enabled")
        
        # Prepare configuration overrides
        config_overrides = {}
        if args.users:
            config_overrides["num_users"] = args.users
        if args.spawn_rate:
            config_overrides["spawn_rate"] = args.spawn_rate
        if args.duration:
            config_overrides["run_time"] = args.duration
        if args.report_dir:
            config_overrides["report_dir"] = args.report_dir
        
        results = []
        
        if args.test_suite:
            # Run test suite
            contexts = [ctx.strip() for ctx in args.contexts.split(",")]
            logger.info(f"Running test suite for contexts: {contexts}")
            
            test_types = [TestType.SMOKE, TestType.LOAD, TestType.STRESS]
            results = orchestrator.run_test_suite(contexts, test_types)
            
        else:
            # Run single test
            test_type = TestType(args.test_type)
            logger.info(f"Running {test_type.value} test for context: {args.context}")
            
            if args.distributed:
                # Run distributed test
                result = run_distributed_test(
                    test_type, args.context, args.environment, 
                    config_overrides, args.workers, args.headless
                )
            else:
                # Run single test
                result = orchestrator.run_test(
                    test_type, args.context, args.environment, config_overrides
                )
            
            results = [result]
        
        # Stop monitoring
        if monitor:
            monitor.stop_monitoring()
            
            if args.export_metrics:
                metrics_file = Path(args.report_dir) / "real_time_metrics.json"
                monitor.export_metrics(str(metrics_file))
                logger.info(f"Real-time metrics exported to {metrics_file}")
        
        # Print results summary
        print_results_summary(results)
        
        # Exit with appropriate code
        failed_tests = [r for r in results if not r.success]
        if failed_tests:
            logger.error(f"{len(failed_tests)} test(s) failed")
            sys.exit(1)
        else:
            logger.info("All tests passed successfully")
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("Test execution interrupted by user")
        if monitor:
            monitor.stop_monitoring()
        sys.exit(130)
        
    except Exception as e:
        logger.error(f"Error during test execution: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def run_distributed_test(test_type: TestType, context: str, environment: Optional[str],
                        config_overrides: dict, workers: int, headless: bool):
    """Run a distributed load test"""
    import subprocess
    import time
    
    logger.info(f"Starting distributed test with {workers} workers")
    
    # Start master process
    master_cmd = [
        sys.executable, "-m", "locust",
        "-f", f"src/locust_tests/{test_type.value}_test.py",
        "--master",
        "--expect-workers", str(workers)
    ]
    
    if headless:
        master_cmd.extend([
            "--headless",
            "--users", str(config_overrides.get("num_users", 100)),
            "--spawn-rate", str(config_overrides.get("spawn_rate", 10)),
            "--run-time", config_overrides.get("run_time", "5m")
        ])
    
    # Set environment variables
    env = os.environ.copy()
    env["CONFIG_CONTEXT"] = context
    if environment:
        env["CONFIG_ENVIRONMENT"] = environment
    
    logger.info(f"Starting master: {' '.join(master_cmd)}")
    master_process = subprocess.Popen(master_cmd, env=env)
    
    # Wait a bit for master to start
    time.sleep(5)
    
    # Start worker processes
    worker_processes = []
    for i in range(workers):
        worker_cmd = [
            sys.executable, "-m", "locust",
            "-f", f"src/locust_tests/{test_type.value}_test.py",
            "--worker",
            "--master-host", "localhost"
        ]
        
        logger.info(f"Starting worker {i+1}: {' '.join(worker_cmd)}")
        worker_process = subprocess.Popen(worker_cmd, env=env)
        worker_processes.append(worker_process)
    
    try:
        # Wait for master to complete
        master_process.wait()
        
        # Terminate workers
        for worker in worker_processes:
            worker.terminate()
            worker.wait(timeout=10)
        
        # Create result object (simplified)
        from locust_framework.test_orchestrator import TestResult
        return TestResult(
            test_type=test_type,
            success=master_process.returncode == 0,
            duration_seconds=0,  # Would need to track this
            total_requests=0,    # Would need to parse from output
            failed_requests=0,
            success_rate=0,
            avg_response_time=0,
            peak_users=config_overrides.get("num_users", 100)
        )
        
    except Exception as e:
        logger.error(f"Error in distributed test: {e}")
        
        # Cleanup processes
        master_process.terminate()
        for worker in worker_processes:
            worker.terminate()
        
        raise


def print_results_summary(results: List):
    """Print a summary of test results"""
    print("\n" + "="*80)
    print("🚀 LOAD TEST RESULTS SUMMARY")
    print("="*80)
    
    total_tests = len(results)
    passed_tests = len([r for r in results if r.success])
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    print("\nDetailed Results:")
    print("-" * 80)
    
    for result in results:
        status_icon = "✅" if result.success else "❌"
        print(f"{status_icon} {result.test_type.value.upper()}")
        print(f"   Duration: {result.duration_seconds:.1f}s")
        print(f"   Requests: {result.total_requests:,} (Failed: {result.failed_requests:,})")
        print(f"   Success Rate: {result.success_rate:.1f}%")
        print(f"   Avg Response Time: {result.avg_response_time:.0f}ms")
        print(f"   Peak Users: {result.peak_users}")
        
        if result.error_message:
            print(f"   Error: {result.error_message}")
        
        if result.report_files:
            print(f"   Reports: {', '.join(result.report_files)}")
        
        print()
    
    print("="*80)


if __name__ == "__main__":
    main()
