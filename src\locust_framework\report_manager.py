"""
Advanced Reporting and Statistics for Locust Load Testing
========================================================

Provides enhanced reporting capabilities with custom statistics,
real-time monitoring, and multiple output formats.
"""

import json
import csv
import time
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict

from locust import events
from locust.stats import StatsEntry

logger = logging.getLogger(__name__)


@dataclass
class CustomStats:
    """Container for custom statistics"""
    test_start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    test_end_time: Optional[datetime] = None
    
    # Request statistics
    total_requests: int = 0
    failed_requests: int = 0
    slow_requests: int = 0  # Requests > threshold
    
    # Response time statistics
    response_times: List[float] = field(default_factory=list)
    slow_request_threshold: float = 5000.0  # ms
    
    # Error tracking
    error_details: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    status_code_counts: Dict[int, int] = field(default_factory=lambda: defaultdict(int))
    
    # User behavior
    active_users: int = 0
    peak_users: int = 0
    user_spawn_rate: float = 0.0
    
    # System metrics (if available)
    cpu_usage: List[float] = field(default_factory=list)
    memory_usage: List[float] = field(default_factory=list)
    
    # Custom metrics
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


class ReportManager:
    """Manages reporting and statistics collection for load tests"""
    
    def __init__(self, report_dir: str = "reports", 
                 slow_request_threshold: float = 5000.0):
        self.report_dir = Path(report_dir)
        self.report_dir.mkdir(exist_ok=True)
        
        self.stats = CustomStats(slow_request_threshold=slow_request_threshold)
        self.test_name = f"load_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Setup event listeners
        self._setup_event_listeners()
        
        logger.info(f"ReportManager initialized. Reports will be saved to: {self.report_dir}")
    
    def _setup_event_listeners(self):
        """Setup Locust event listeners for statistics collection"""
        
        @events.request.add_listener
        def on_request(request_type, name, response_time, response_length, 
                      response, context, exception, **kwargs):
            """Handle request events"""
            self.stats.total_requests += 1
            
            if response_time:
                self.stats.response_times.append(response_time)
                
                # Track slow requests
                if response_time > self.stats.slow_request_threshold:
                    self.stats.slow_requests += 1
            
            # Track status codes
            if response and hasattr(response, 'status_code'):
                self.stats.status_code_counts[response.status_code] += 1
            
            # Track errors
            if exception:
                self.stats.failed_requests += 1
                error_key = f"{type(exception).__name__}: {str(exception)[:100]}"
                self.stats.error_details[error_key] += 1
            elif response and hasattr(response, 'status_code') and response.status_code >= 400:
                self.stats.failed_requests += 1
                error_key = f"HTTP_{response.status_code}"
                self.stats.error_details[error_key] += 1
        
        @events.user_add.add_listener
        def on_user_add(user_instance, **kwargs):
            """Handle user spawn events"""
            self.stats.active_users += 1
            if self.stats.active_users > self.stats.peak_users:
                self.stats.peak_users = self.stats.active_users
        
        @events.user_remove.add_listener
        def on_user_remove(user_instance, **kwargs):
            """Handle user removal events"""
            self.stats.active_users = max(0, self.stats.active_users - 1)
        
        @events.test_start.add_listener
        def on_test_start(environment, **kwargs):
            """Handle test start"""
            self.stats.test_start_time = datetime.now(timezone.utc)
            logger.info("🚀 Load test started - statistics collection active")
        
        @events.test_stop.add_listener
        def on_test_stop(environment, **kwargs):
            """Handle test stop and generate reports"""
            self.stats.test_end_time = datetime.now(timezone.utc)
            logger.info("🏁 Load test completed - generating reports")
            
            # Generate all reports
            self.generate_reports(environment)
    
    def add_custom_metric(self, name: str, value: Any):
        """Add a custom metric to the statistics"""
        self.stats.custom_metrics[name] = value
        logger.debug(f"Added custom metric: {name} = {value}")
    
    def record_system_metrics(self, cpu_percent: float, memory_percent: float):
        """Record system performance metrics"""
        self.stats.cpu_usage.append(cpu_percent)
        self.stats.memory_usage.append(memory_percent)
    
    def generate_reports(self, environment):
        """Generate all report formats"""
        try:
            # Generate JSON report
            self.generate_json_report()
            
            # Generate CSV report
            self.generate_csv_report(environment)
            
            # Generate HTML report
            self.generate_html_report(environment)
            
            # Generate summary report
            self.generate_summary_report()
            
            logger.info(f"All reports generated in: {self.report_dir}")
            
        except Exception as e:
            logger.error(f"Error generating reports: {e}")
    
    def generate_json_report(self):
        """Generate detailed JSON report"""
        report_data = {
            "test_info": {
                "test_name": self.test_name,
                "start_time": self.stats.test_start_time.isoformat(),
                "end_time": self.stats.test_end_time.isoformat() if self.stats.test_end_time else None,
                "duration_seconds": (
                    (self.stats.test_end_time - self.stats.test_start_time).total_seconds()
                    if self.stats.test_end_time else 0
                )
            },
            "request_stats": {
                "total_requests": self.stats.total_requests,
                "failed_requests": self.stats.failed_requests,
                "success_rate": (
                    (self.stats.total_requests - self.stats.failed_requests) / self.stats.total_requests * 100
                    if self.stats.total_requests > 0 else 0
                ),
                "slow_requests": self.stats.slow_requests,
                "slow_request_threshold_ms": self.stats.slow_request_threshold
            },
            "response_times": {
                "average": sum(self.stats.response_times) / len(self.stats.response_times) if self.stats.response_times else 0,
                "min": min(self.stats.response_times) if self.stats.response_times else 0,
                "max": max(self.stats.response_times) if self.stats.response_times else 0,
                "percentiles": self._calculate_percentiles(self.stats.response_times)
            },
            "user_stats": {
                "peak_users": self.stats.peak_users,
                "final_active_users": self.stats.active_users
            },
            "error_details": dict(self.stats.error_details),
            "status_codes": dict(self.stats.status_code_counts),
            "system_metrics": {
                "avg_cpu_usage": sum(self.stats.cpu_usage) / len(self.stats.cpu_usage) if self.stats.cpu_usage else 0,
                "avg_memory_usage": sum(self.stats.memory_usage) / len(self.stats.memory_usage) if self.stats.memory_usage else 0,
                "peak_cpu_usage": max(self.stats.cpu_usage) if self.stats.cpu_usage else 0,
                "peak_memory_usage": max(self.stats.memory_usage) if self.stats.memory_usage else 0
            },
            "custom_metrics": self.stats.custom_metrics
        }
        
        json_file = self.report_dir / f"{self.test_name}_detailed.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"JSON report saved: {json_file}")
    
    def generate_csv_report(self, environment):
        """Generate CSV report with request statistics"""
        csv_file = self.report_dir / f"{self.test_name}_requests.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Header
            writer.writerow([
                'Name', 'Method', 'Request Count', 'Failure Count', 
                'Average Response Time', 'Min Response Time', 'Max Response Time',
                '50th Percentile', '95th Percentile', '99th Percentile',
                'Requests/sec', 'Failures/sec'
            ])
            
            # Data from Locust stats
            if hasattr(environment, 'stats') and environment.stats.entries:
                for name, entry in environment.stats.entries.items():
                    if isinstance(entry, StatsEntry):
                        writer.writerow([
                            entry.name,
                            entry.method,
                            entry.num_requests,
                            entry.num_failures,
                            entry.avg_response_time,
                            entry.min_response_time,
                            entry.max_response_time,
                            entry.get_response_time_percentile(0.5),
                            entry.get_response_time_percentile(0.95),
                            entry.get_response_time_percentile(0.99),
                            entry.current_rps,
                            entry.current_fail_per_sec
                        ])
        
        logger.info(f"CSV report saved: {csv_file}")
    
    def generate_html_report(self, environment):
        """Generate HTML report with charts and visualizations"""
        html_content = self._create_html_report_content(environment)
        
        html_file = self.report_dir / f"{self.test_name}_report.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML report saved: {html_file}")
    
    def generate_summary_report(self):
        """Generate a concise summary report"""
        duration = (
            (self.stats.test_end_time - self.stats.test_start_time).total_seconds()
            if self.stats.test_end_time else 0
        )
        
        success_rate = (
            (self.stats.total_requests - self.stats.failed_requests) / self.stats.total_requests * 100
            if self.stats.total_requests > 0 else 0
        )
        
        avg_response_time = (
            sum(self.stats.response_times) / len(self.stats.response_times)
            if self.stats.response_times else 0
        )
        
        summary = f"""
📊 LOAD TEST SUMMARY
==================
Test: {self.test_name}
Duration: {duration:.1f} seconds
Peak Users: {self.stats.peak_users}

📈 REQUEST STATISTICS
Total Requests: {self.stats.total_requests:,}
Failed Requests: {self.stats.failed_requests:,}
Success Rate: {success_rate:.1f}%
Slow Requests: {self.stats.slow_requests:,} (>{self.stats.slow_request_threshold}ms)

⏱️ RESPONSE TIMES
Average: {avg_response_time:.0f}ms
Min: {min(self.stats.response_times) if self.stats.response_times else 0:.0f}ms
Max: {max(self.stats.response_times) if self.stats.response_times else 0:.0f}ms

🚨 TOP ERRORS
{self._format_top_errors()}

💻 SYSTEM METRICS
Avg CPU: {sum(self.stats.cpu_usage) / len(self.stats.cpu_usage) if self.stats.cpu_usage else 0:.1f}%
Avg Memory: {sum(self.stats.memory_usage) / len(self.stats.memory_usage) if self.stats.memory_usage else 0:.1f}%
"""
        
        summary_file = self.report_dir / f"{self.test_name}_summary.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        # Also print to console
        print(summary)
        
        logger.info(f"Summary report saved: {summary_file}")
    
    def _calculate_percentiles(self, values: List[float]) -> Dict[str, float]:
        """Calculate response time percentiles"""
        if not values:
            return {}
        
        sorted_values = sorted(values)
        n = len(sorted_values)
        
        percentiles = {}
        for p in [50, 75, 90, 95, 99]:
            index = int(n * p / 100) - 1
            if index < 0:
                index = 0
            elif index >= n:
                index = n - 1
            percentiles[f"p{p}"] = sorted_values[index]
        
        return percentiles
    
    def _format_top_errors(self, limit: int = 5) -> str:
        """Format top errors for summary report"""
        if not self.stats.error_details:
            return "No errors recorded"
        
        sorted_errors = sorted(
            self.stats.error_details.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        lines = []
        for error, count in sorted_errors[:limit]:
            lines.append(f"  {error}: {count}")
        
        return "\n".join(lines)
    
    def _create_html_report_content(self, environment) -> str:
        """Create enhanced HTML report content with charts and better styling"""
        percentiles = self._calculate_percentiles(self.stats.response_times)
        duration = (self.stats.test_end_time - self.stats.test_start_time).total_seconds() if self.stats.test_end_time else 0
        success_rate = (self.stats.total_requests - self.stats.failed_requests) / self.stats.total_requests * 100 if self.stats.total_requests > 0 else 0

        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>Load Test Report - {self.test_name}</title>
    <meta charset="utf-8">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 20px; }}
        .header h1 {{ margin: 0 0 10px 0; font-size: 2.5em; }}
        .header p {{ margin: 5px 0; opacity: 0.9; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
        .stat-card {{ background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .stat-card h3 {{ margin: 0 0 15px 0; color: #333; font-size: 1.2em; }}
        .stat-value {{ font-size: 2em; font-weight: bold; margin: 10px 0; }}
        .stat-label {{ color: #666; font-size: 0.9em; }}
        .success {{ color: #4caf50; }}
        .warning {{ color: #ff9800; }}
        .error {{ color: #f44336; }}
        .info {{ color: #2196f3; }}
        .chart-container {{ background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0; }}
        .table-container {{ background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0; overflow: hidden; }}
        table {{ width: 100%; border-collapse: collapse; }}
        th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; color: #333; }}
        td {{ padding: 12px 15px; border-bottom: 1px solid #eee; }}
        tr:hover {{ background: #f8f9fa; }}
        .progress-bar {{ width: 100%; height: 20px; background: #eee; border-radius: 10px; overflow: hidden; }}
        .progress-fill {{ height: 100%; background: linear-gradient(90deg, #4caf50, #8bc34a); transition: width 0.3s; }}
        .metric-trend {{ display: flex; align-items: center; gap: 10px; }}
        .trend-up {{ color: #f44336; }}
        .trend-down {{ color: #4caf50; }}
        .section {{ margin: 30px 0; }}
        .section h2 {{ color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Load Test Report</h1>
            <p><strong>Test Name:</strong> {self.test_name}</p>
            <p><strong>Start Time:</strong> {self.stats.test_start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
            <p><strong>Duration:</strong> {duration:.1f} seconds ({duration/60:.1f} minutes)</p>
            <p><strong>Status:</strong> <span class="{'success' if success_rate > 95 else 'warning' if success_rate > 90 else 'error'}">
                {'✅ PASSED' if success_rate > 95 else '⚠️ WARNING' if success_rate > 90 else '❌ FAILED'}
            </span></p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 Request Statistics</h3>
                <div class="stat-value info">{self.stats.total_requests:,}</div>
                <div class="stat-label">Total Requests</div>
                <div class="stat-value error">{self.stats.failed_requests:,}</div>
                <div class="stat-label">Failed Requests</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {success_rate}%"></div>
                </div>
                <div class="stat-label">Success Rate: {success_rate:.1f}%</div>
            </div>

            <div class="stat-card">
                <h3>⏱️ Response Times</h3>
                <div class="stat-value info">{sum(self.stats.response_times) / len(self.stats.response_times) if self.stats.response_times else 0:.0f}ms</div>
                <div class="stat-label">Average Response Time</div>
                <div class="metric-trend">
                    <span>Min: {min(self.stats.response_times) if self.stats.response_times else 0:.0f}ms</span>
                    <span>Max: {max(self.stats.response_times) if self.stats.response_times else 0:.0f}ms</span>
                </div>
                <div class="stat-value warning">{self.stats.slow_requests:,}</div>
                <div class="stat-label">Slow Requests (>{self.stats.slow_request_threshold}ms)</div>
            </div>

            <div class="stat-card">
                <h3>👥 User Statistics</h3>
                <div class="stat-value success">{self.stats.peak_users}</div>
                <div class="stat-label">Peak Concurrent Users</div>
                <div class="stat-value info">{self.stats.active_users}</div>
                <div class="stat-label">Final Active Users</div>
                <div class="stat-value">{self.stats.total_requests / duration if duration > 0 else 0:.1f}</div>
                <div class="stat-label">Requests per Second</div>
            </div>

            <div class="stat-card">
                <h3>💻 System Metrics</h3>
                <div class="stat-value info">{sum(self.stats.cpu_usage) / len(self.stats.cpu_usage) if self.stats.cpu_usage else 0:.1f}%</div>
                <div class="stat-label">Average CPU Usage</div>
                <div class="stat-value warning">{sum(self.stats.memory_usage) / len(self.stats.memory_usage) if self.stats.memory_usage else 0:.1f}%</div>
                <div class="stat-label">Average Memory Usage</div>
                <div class="metric-trend">
                    <span>Peak CPU: {max(self.stats.cpu_usage) if self.stats.cpu_usage else 0:.1f}%</span>
                    <span>Peak Memory: {max(self.stats.memory_usage) if self.stats.memory_usage else 0:.1f}%</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 Response Time Distribution</h2>
            <div class="chart-container">
                <canvas id="responseTimeChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="section">
            <h2>🚨 Error Analysis</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Error Type</th>
                            <th>Count</th>
                            <th>Percentage</th>
                            <th>Impact</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join(f'''<tr>
                            <td>{error}</td>
                            <td>{count:,}</td>
                            <td>{count/self.stats.total_requests*100 if self.stats.total_requests > 0 else 0:.2f}%</td>
                            <td><span class="{'error' if count/self.stats.total_requests > 0.05 else 'warning' if count/self.stats.total_requests > 0.01 else 'success'}">
                                {'High' if count/self.stats.total_requests > 0.05 else 'Medium' if count/self.stats.total_requests > 0.01 else 'Low'}
                            </span></td>
                        </tr>''' for error, count in sorted(self.stats.error_details.items(), key=lambda x: x[1], reverse=True))}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="section">
            <h2>📊 HTTP Status Codes</h2>
            <div class="chart-container">
                <canvas id="statusCodeChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Performance Percentiles</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Percentile</th>
                            <th>Response Time (ms)</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join(f'''<tr>
                            <td>{p}</td>
                            <td>{value:.0f}ms</td>
                            <td><span class="{'success' if value < 1000 else 'warning' if value < 5000 else 'error'}">
                                {'Excellent' if value < 1000 else 'Good' if value < 5000 else 'Poor'}
                            </span></td>
                        </tr>''' for p, value in percentiles.items())}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Response Time Distribution Chart
        const rtCtx = document.getElementById('responseTimeChart').getContext('2d');
        new Chart(rtCtx, {{
            type: 'line',
            data: {{
                labels: {list(percentiles.keys())},
                datasets: [{{
                    label: 'Response Time (ms)',
                    data: {list(percentiles.values())},
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Response Time Percentiles'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: 'Response Time (ms)'
                        }}
                    }}
                }}
            }}
        }});

        // Status Code Distribution Chart
        const scCtx = document.getElementById('statusCodeChart').getContext('2d');
        new Chart(scCtx, {{
            type: 'doughnut',
            data: {{
                labels: {list(self.stats.status_code_counts.keys())},
                datasets: [{{
                    data: {list(self.stats.status_code_counts.values())},
                    backgroundColor: [
                        '#4caf50', '#2196f3', '#ff9800', '#f44336', '#9c27b0'
                    ]
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'HTTP Status Code Distribution'
                    }},
                    legend: {{
                        position: 'bottom'
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
"""
