#!/usr/bin/env python3
"""
Baseline performance test - Establish performance benchmarks
"""
import asyncio
import subprocess
import json
import time
import statistics
from datetime import datetime
import argparse

class BaselineTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)

    async def run_baseline_tests(self):
        """Run comprehensive baseline tests"""
        print("📊 Starting baseline performance tests...")
        
        tests = [
            ("Single User", 1),
            ("Low Load", 10),
            ("Medium Load", 50),
            ("High Load", 100)
        ]
        
        results = []
        
        for test_name, users in tests:
            print(f"\n🔍 Running {test_name} test ({users} users)...")
            
            # Run test multiple times for statistical accuracy
            run_results = []
            for run in range(3):
                print(f"   Run {run + 1}/3...")
                result = await self._run_baseline_test(users)
                run_results.append(result)
                await asyncio.sleep(10)  # Cool down between runs
            
            # Calculate statistics
            response_times = [r["avg_response_time"] for r in run_results if r["avg_response_time"] > 0]
            success_rates = [r["success_rate"] for r in run_results]
            
            baseline_result = {
                "test_name": test_name,
                "users": users,
                "runs": len(run_results),
                "avg_response_time": statistics.mean(response_times) if response_times else 0,
                "median_response_time": statistics.median(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "avg_success_rate": statistics.mean(success_rates),
                "min_success_rate": min(success_rates),
                "raw_results": run_results
            }
            
            results.append(baseline_result)
            
            print(f"   ✅ Avg Response Time: {baseline_result['avg_response_time']:.3f}s")
            print(f"   ✅ Avg Success Rate: {baseline_result['avg_success_rate']:.1f}%")
        
        await self._save_baseline_results(results)
        return results

    async def _run_baseline_test(self, users: int):
        """Run single baseline test"""
        # Create test config
        test_config = self.config.copy()
        test_config['num_threads'] = users
        
        temp_config = f"temp_baseline_{users}.json"
        with open(temp_config, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        try:
            start_time = time.time()
            
            result = subprocess.run([
                "python", "-m", "src.main",
                "-c", temp_config,
                "--skip-jwt-validation"
            ], capture_output=True, text=True, timeout=300)
            
            duration = time.time() - start_time
            
            # Parse results from output
            success_rate = self._parse_success_rate(result.stdout)
            avg_response_time = self._parse_avg_response_time(result.stdout)
            
            return {
                "users": users,
                "duration": duration,
                "success_rate": success_rate,
                "avg_response_time": avg_response_time,
                "stdout": result.stdout[:1000],  # First 1000 chars
                "stderr": result.stderr[:500] if result.stderr else ""
            }
            
        except subprocess.TimeoutExpired:
            return {
                "users": users,
                "duration": 300,
                "success_rate": 0,
                "avg_response_time": 0,
                "error": "Test timed out"
            }
        except Exception as e:
            return {
                "users": users,
                "duration": 0,
                "success_rate": 0,
                "avg_response_time": 0,
                "error": str(e)
            }
        finally:
            import os
            if os.path.exists(temp_config):
                os.remove(temp_config)

    def _parse_success_rate(self, output: str) -> float:
        """Parse success rate from test output"""
        import re
        # Look for patterns like "95.5% success" or "Success rate: 95.5%"
        patterns = [
            r'(\d+\.?\d*)%.*success',
            r'success.*?(\d+\.?\d*)%',
            r'Success rate.*?(\d+\.?\d*)%'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, output, re.IGNORECASE)
            if match:
                return float(match.group(1))
        
        return 100.0  # Default if not found

    def _parse_avg_response_time(self, output: str) -> float:
        """Parse average response time from test output"""
        import re
        # Look for patterns like "avg: 1.234s" or "Average response time: 1.234"
        patterns = [
            r'avg.*?(\d+\.?\d*)\s*s',
            r'average.*?(\d+\.?\d*)\s*s',
            r'response time.*?(\d+\.?\d*)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, output, re.IGNORECASE)
            if match:
                return float(match.group(1))
        
        return 0.0

    async def _save_baseline_results(self, results: list):
        """Save baseline test results"""
        filename = f"baseline_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Create performance benchmarks
        benchmarks = {}
        for result in results:
            benchmarks[f"{result['users']}_users"] = {
                "target_response_time": result['avg_response_time'] * 1.2,  # 20% tolerance
                "min_success_rate": max(95.0, result['avg_success_rate'] - 5.0)  # 5% tolerance
            }
        
        report = {
            "test_type": "baseline_test",
            "test_date": datetime.now().isoformat(),
            "results": results,
            "benchmarks": benchmarks,
            "summary": {
                "single_user_response_time": next((r['avg_response_time'] for r in results if r['users'] == 1), 0),
                "max_tested_users": max([r['users'] for r in results]),
                "best_success_rate": max([r['avg_success_rate'] for r in results])
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📋 Baseline test results saved to {filename}")
        print(f"📈 Performance benchmarks established for future comparisons")

async def main():
    parser = argparse.ArgumentParser(description="Baseline performance testing")
    parser.add_argument("--config", default="config.global.json", help="Configuration file")
    
    args = parser.parse_args()
    
    tester = BaselineTester(args.config)
    await tester.run_baseline_tests()

if __name__ == "__main__":
    asyncio.run(main())