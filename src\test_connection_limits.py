#!/usr/bin/env python3
"""
Test connection pool limits and concurrent connection handling
"""
import asyncio
import httpx
import json
import time
from datetime import datetime
import argparse

class ConnectionPoolTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.base_url = self.config["base_url"]
        self.jwt_token = self.config["jwt_token"]
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}
        self.results = []

    async def test_connection_limits(self, max_connections: int = 10):
        """Test maximum concurrent connections"""
        print(f"🔗 Testing connection limits with {max_connections} concurrent connections")
        
        # Test with different connection pool sizes
        pool_sizes = [5, 10, 20, 50, 100]
        
        for pool_size in pool_sizes:
            if pool_size > max_connections:
                continue
                
            print(f"\n📊 Testing with connection pool size: {pool_size}")
            
            limits = httpx.Limits(
                max_keepalive_connections=pool_size,
                max_connections=pool_size * 2,
                keepalive_expiry=30.0
            )
            
            async with httpx.AsyncClient(
                limits=limits,
                timeout=httpx.Timeout(30.0),
                verify=self.config.get("verify_ssl", True)
            ) as client:
                
                start_time = time.time()
                tasks = []
                
                # Create concurrent requests
                for i in range(max_connections):
                    task = asyncio.create_task(
                        self._make_test_request(client, i)
                    )
                    tasks.append(task)
                
                # Wait for all requests to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)
                duration = time.time() - start_time
                
                # Analyze results
                successful = sum(1 for r in results if not isinstance(r, Exception))
                failed = len(results) - successful
                
                result = {
                    "pool_size": pool_size,
                    "max_connections": max_connections,
                    "successful": successful,
                    "failed": failed,
                    "success_rate": (successful / len(results)) * 100,
                    "duration": duration,
                    "avg_time_per_request": duration / len(results)
                }
                
                self.results.append(result)
                
                print(f"✅ Pool {pool_size}: {successful}/{max_connections} successful ({result['success_rate']:.1f}%)")
                print(f"   Duration: {duration:.2f}s, Avg per request: {result['avg_time_per_request']:.3f}s")
                
                # Log failures
                for i, r in enumerate(results):
                    if isinstance(r, Exception):
                        print(f"   ❌ Request {i}: {type(r).__name__}: {str(r)}")
        
        await self._save_results()

    async def _make_test_request(self, client: httpx.AsyncClient, request_id: int):
        """Make a single test request"""
        try:
            response = await client.get(
                f"{self.base_url}/health",
                headers=self.headers
            )
            
            if response.status_code == 200:
                return {"request_id": request_id, "status": "success", "response_time": response.elapsed.total_seconds()}
            else:
                return {"request_id": request_id, "status": "failed", "status_code": response.status_code}
                
        except Exception as e:
            raise e

    async def test_connection_reuse(self):
        """Test connection reuse efficiency"""
        print("\n🔄 Testing connection reuse efficiency")
        
        # Test with keep-alive vs without
        test_configs = [
            {"keep_alive": True, "name": "With Keep-Alive"},
            {"keep_alive": False, "name": "Without Keep-Alive"}
        ]
        
        for config in test_configs:
            print(f"\n📈 {config['name']}")
            
            if config["keep_alive"]:
                limits = httpx.Limits(max_keepalive_connections=10, keepalive_expiry=30.0)
            else:
                limits = httpx.Limits(max_keepalive_connections=0)
            
            async with httpx.AsyncClient(limits=limits, verify=self.config.get("verify_ssl", True)) as client:
                start_time = time.time()
                
                # Make 20 sequential requests
                for i in range(20):
                    try:
                        response = await client.get(f"{self.base_url}/health", headers=self.headers)
                        if i % 5 == 0:
                            print(f"   Request {i+1}: {response.status_code}")
                    except Exception as e:
                        print(f"   Request {i+1}: Error - {e}")
                
                duration = time.time() - start_time
                print(f"   Total time: {duration:.2f}s, Avg per request: {duration/20:.3f}s")

    async def _save_results(self):
        """Save test results to file"""
        filename = f"connection_pool_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "test_date": datetime.now().isoformat(),
            "config": {
                "base_url": self.base_url,
                "verify_ssl": self.config.get("verify_ssl", True)
            },
            "results": self.results
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📋 Connection pool test results saved to {filename}")

async def main():
    parser = argparse.ArgumentParser(description="Test connection pool limits")
    parser.add_argument("--max-connections", type=int, default=10, help="Maximum concurrent connections to test")
    parser.add_argument("--config", default="config.global.json", help="Configuration file path")
    
    args = parser.parse_args()
    
    tester = ConnectionPoolTester(args.config)
    await tester.test_connection_limits(args.max_connections)
    await tester.test_connection_reuse()

if __name__ == "__main__":
    asyncio.run(main())