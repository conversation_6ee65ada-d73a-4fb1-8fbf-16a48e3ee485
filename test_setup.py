#!/usr/bin/env python3
"""
Quick setup test for the enhanced load testing framework
"""

import sys
import json
from pathlib import Path

def test_basic_setup():
    """Test basic setup without complex imports"""
    print("🔍 Testing Enhanced Load Testing Framework Setup")
    print("=" * 50)
    
    # Test 1: Check Python version
    print(f"✅ Python version: {sys.version}")
    
    # Test 2: Check if config files exist
    config_files = [
        "config.global.json",
        "configs/andoc.json", 
        "configs/talperftraitement.json",
        "configs/environments.json"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ Configuration file found: {config_file}")
        else:
            print(f"❌ Configuration file missing: {config_file}")
    
    # Test 3: Check if framework files exist
    framework_files = [
        "src/locust_framework/__init__.py",
        "src/locust_framework/config_manager.py",
        "src/locust_framework/base_user.py",
        "src/locust_tests/load_test.py",
        "scripts/run_load_tests.py"
    ]
    
    for framework_file in framework_files:
        if Path(framework_file).exists():
            print(f"✅ Framework file found: {framework_file}")
        else:
            print(f"❌ Framework file missing: {framework_file}")
    
    # Test 4: Try to load global config
    try:
        with open("config.global.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        print("✅ Global configuration loaded successfully")
        print(f"   Base URL: {config.get('base_url', 'Not set')}")
        print(f"   JWT Token: {'Set' if config.get('jwt_token') else 'Not set'}")
        print(f"   SSL Verification: {config.get('verify_ssl', 'Not set')}")
        
    except Exception as e:
        print(f"❌ Error loading global configuration: {e}")
    
    # Test 5: Check if we can import basic Python modules
    try:
        import json
        import pathlib
        import logging
        print("✅ Basic Python modules available")
    except Exception as e:
        print(f"❌ Error importing basic modules: {e}")
    
    print("\n🎯 Next Steps:")
    print("1. Activate the uv environment: uv shell")
    print("2. Or run commands with: uv run python <script>")
    print("3. Test JWT validation: uv run python scripts/manage_config.py --validate-jwt")
    print("4. Run first smoke test: uv run python scripts/run_load_tests.py --test-type smoke --context andoc")

if __name__ == "__main__":
    test_basic_setup()
