Test with different worker counts: Compare performance with 1, 2, 4, 8 workers
Monitor resource usage: CPU, memory, network during tests
Validate connection limits: Test maximum concurrent connections
Check error patterns: Analyze if errors increase with load
Test failover scenarios: What happens when AOAI is unavailable
Measure response time degradation: How performance changes with load
Validate JWT token refresh: Test behavior when tokens expire during load
Test against local environment: Compare local vs remote performance