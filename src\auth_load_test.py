#!/usr/bin/env python3
"""
Authentication & Authorization Load Test - Test JWT handling under load
"""
import asyncio
import httpx
import json
import time
from datetime import datetime
import argparse

class AuthLoadTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.base_url = self.config["base_url"]

    async def run_auth_load_test(self, concurrent_users: int = 500):
        """Test concurrent authentication and JWT validation"""
        print(f"🔐 Starting auth load test with {concurrent_users} users...")
        
        # Test scenarios
        tests = [
            ("JWT Validation Load", self._test_jwt_validation_load),
            ("Token Refresh Under Load", self._test_token_refresh_load),
            ("Permission Check Load", self._test_permission_load),
            ("Session Creation Load", self._test_session_creation_load)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            result = await test_func(concurrent_users)
            result["test_name"] = test_name
            results.append(result)
        
        await self._save_auth_results(results)

    async def _test_jwt_validation_load(self, users: int):
        """Test JWT validation under concurrent load"""
        headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.config['jwt_token']}"}
        
        async def make_auth_request(session_id):
            async with httpx.AsyncClient(timeout=30.0) as client:
                try:
                    response = await client.get(f"{self.base_url}/health", headers=headers)
                    return {"status": "success", "response_time": response.elapsed.total_seconds()}
                except Exception as e:
                    return {"status": "error", "error": str(e)}
        
        # Run concurrent requests
        tasks = [make_auth_request(i) for i in range(users)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if isinstance(r, dict) and r.get("status") == "success")
        return {"success_rate": (success_count / users) * 100, "total_requests": users}

if __name__ == "__main__":
    asyncio.run(AuthLoadTester().run_auth_load_test())