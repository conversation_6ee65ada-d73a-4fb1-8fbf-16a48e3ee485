# Enhanced Locust Load Testing Framework

A comprehensive, enterprise-grade load testing framework built on top of Locust with advanced features for modern API testing, monitoring, and reporting.

## 🚀 Features

### Core Capabilities
- **Enhanced User Classes**: JWT authentication, session management, and advanced request handling
- **Specialized Test Scenarios**: Load, stress, spike, endurance, smoke, and baseline testing
- **Advanced Configuration Management**: Environment-specific configs, context switching, and validation
- **Comprehensive Reporting**: HTML reports with charts, CSV exports, and JSON metrics
- **Real-time Monitoring**: Live metrics, alerting, and system resource tracking
- **Monitoring Integrations**: Prometheus, InfluxDB, Slack, email, and webhook support

### Test Types
- **🔍 Smoke Tests**: Basic functionality verification
- **📊 Load Tests**: Normal expected load simulation
- **💥 Stress Tests**: Beyond-capacity testing to find breaking points
- **⚡ Spike Tests**: Sudden load increase simulation
- **⏰ Endurance Tests**: Long-duration stability testing
- **📈 Scalability Tests**: Gradual load increase analysis
- **🎯 Baseline Tests**: Single-user performance benchmarking

## 📁 Project Structure

```
├── src/
│   ├── locust_framework/           # Enhanced framework core
│   │   ├── __init__.py
│   │   ├── base_user.py           # Enhanced user classes
│   │   ├── config_manager.py      # Configuration management
│   │   ├── report_manager.py      # Advanced reporting
│   │   ├── test_orchestrator.py   # Test execution coordination
│   │   ├── real_time_monitor.py   # Live monitoring
│   │   ├── monitoring_integrations.py # External integrations
│   │   └── utils.py               # Utilities and helpers
│   ├── locust_tests/              # Specialized test scenarios
│   │   ├── load_test.py
│   │   ├── stress_test.py
│   │   ├── spike_test.py
│   │   ├── endurance_test.py
│   │   └── smoke_test.py
│   └── locustfile.py              # Enhanced main locust file
├── scripts/
│   ├── run_load_tests.py          # Main orchestration script
│   ├── enhanced-load-test.ps1     # PowerShell interface
│   ├── manage_config.py           # Configuration management
│   └── setup_monitoring.py       # Monitoring setup
├── configs/
│   ├── andoc.json                 # Context-specific configs
│   ├── talperftraitement.json
│   └── environments.json         # Environment configs
├── config.global.json             # Global configuration
├── reports/                       # Generated reports
├── logs/                          # Test logs
└── docs/                          # Documentation
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd load-testing-framework

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -e .
```

### 2. Configuration

Update `config.global.json` with your API details:

```json
{
  "base_url": "https://your-api.com",
  "jwt_token": "your_jwt_token_here",
  "verify_ssl": false,
  "num_users": 10,
  "spawn_rate": 1.0,
  "run_time": "5m"
}
```

### 3. Run Your First Test

```bash
# Simple smoke test
python scripts/run_load_tests.py --test-type smoke --context andoc

# Load test with monitoring
python scripts/run_load_tests.py --test-type load --context andoc --monitor

# Full test suite
python scripts/run_load_tests.py --test-suite --contexts andoc,talperftraitement
```

### 4. Using PowerShell (Windows)

```powershell
# Run load test
.\scripts\enhanced-load-test.ps1 -TestType load -Context andoc -Users 50

# Run with web UI
.\scripts\enhanced-load-test.ps1 -TestType load -Context andoc -WebUI

# Comprehensive test suite
.\scripts\enhanced-load-test.ps1 -TestSuite -Contexts "andoc,talperftraitement"
```

## 📖 Detailed Usage

### Configuration Management

The framework uses a hierarchical configuration system:

1. **Global Config** (`config.global.json`): Base settings
2. **Environment Config** (`configs/environments.json`): Environment-specific overrides
3. **Context Config** (`configs/{context}.json`): Context-specific settings
4. **Runtime Overrides**: Command-line parameters

```bash
# Validate all configurations
python scripts/manage_config.py --validate-all

# Show merged configuration
python scripts/manage_config.py --show-config --context andoc --environment production

# Create new context
python scripts/manage_config.py --create-context myapp --base-url https://api.myapp.com
```

### Test Scenarios

#### Load Testing
```bash
# Standard load test
python scripts/run_load_tests.py --test-type load --context andoc --users 100 --duration 10m

# With real-time monitoring
python scripts/run_load_tests.py --test-type load --context andoc --users 100 --monitor
```

#### Stress Testing
```bash
# Push beyond normal capacity
python scripts/run_load_tests.py --test-type stress --context andoc --users 500 --duration 15m
```

#### Spike Testing
```bash
# Sudden load increases
python scripts/run_load_tests.py --test-type spike --context andoc --users 200 --spawn-rate 50
```

#### Endurance Testing
```bash
# Long-duration stability test
python scripts/run_load_tests.py --test-type endurance --context andoc --users 50 --duration 2h
```

### Distributed Testing

```bash
# Run distributed test with multiple workers
python scripts/run_load_tests.py --test-type load --context andoc --distributed --workers 4

# Manual distributed setup
# Terminal 1 (Master):
locust -f src/locust_tests/load_test.py --master --host https://your-api.com

# Terminal 2-N (Workers):
locust -f src/locust_tests/load_test.py --worker --master-host localhost
```

### Monitoring and Alerting

#### Setup Monitoring
```bash
# Setup Prometheus integration
python scripts/setup_monitoring.py --prometheus --pushgateway-url http://localhost:9091

# Setup Slack alerts
python scripts/setup_monitoring.py --slack --webhook-url https://hooks.slack.com/...

# Setup from configuration file
python scripts/setup_monitoring.py --config monitoring_config.json
```

#### Real-time Monitoring
```python
from locust_framework.real_time_monitor import RealTimeMonitor

# Start monitoring
monitor = RealTimeMonitor()
monitor.start_monitoring()

# Add custom alert rules
monitor.alert_manager.add_alert_rule(
    name="high_error_rate",
    condition=lambda s: s.failures_per_second > s.requests_per_second * 0.1,
    message="Error rate exceeds 10%",
    severity="critical"
)
```

## 🔧 Advanced Configuration

### Custom User Classes

```python
from locust_framework import AuthenticatedUser
from locust import task, between

class MyAPIUser(AuthenticatedUser):
    wait_time = between(1, 3)
    
    @task(3)
    def api_call(self):
        with self.authenticated_request("GET", "/api/data") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"API call failed: {response.status_code}")
    
    @task(1)
    def upload_file(self):
        files = {"file": ("test.txt", "test content", "text/plain")}
        with self.authenticated_request("POST", "/api/upload", files=files) as response:
            if response.status_code == 201:
                response.success()
            else:
                response.failure(f"Upload failed: {response.status_code}")
```

### Environment-Specific Configuration

```json
// configs/environments.json
{
  "development": {
    "base_url": "https://dev-api.example.com",
    "num_users": 5,
    "verify_ssl": false
  },
  "staging": {
    "base_url": "https://staging-api.example.com",
    "num_users": 50,
    "verify_ssl": true
  },
  "production": {
    "base_url": "https://api.example.com",
    "num_users": 100,
    "verify_ssl": true
  }
}
```

### Context-Specific Endpoints

```json
// configs/myapp.json
{
  "endpoints": [
    {
      "url": "/api/session/new",
      "method": "POST",
      "id": "create_session",
      "extract": [
        {"key": "session_id", "valueFrom": "session_id"}
      ]
    },
    {
      "url": "/api/data",
      "method": "GET",
      "depends_on": "create_session",
      "params": {
        "session_id": "{session_id}"
      }
    }
  ]
}
```

## 📊 Reporting and Analysis

### HTML Reports
The framework generates comprehensive HTML reports with:
- Interactive charts and visualizations
- Response time percentiles
- Error analysis and categorization
- System metrics tracking
- Custom styling and branding

### CSV Exports
Detailed CSV files for further analysis:
- Request statistics by endpoint
- Response time distributions
- Error details and patterns
- System resource usage

### JSON Metrics
Machine-readable metrics for integration:
- Real-time monitoring data
- Test execution metadata
- Custom metrics and KPIs
- Alert history and patterns

## 🔍 Troubleshooting

### Common Issues

#### JWT Token Expired
```bash
# Validate token
python scripts/manage_config.py --validate-jwt

# Update token
python scripts/manage_config.py --update-jwt "new_token_here"
```

#### Configuration Errors
```bash
# Validate all configurations
python scripts/manage_config.py --validate-all

# Show merged config for debugging
python scripts/manage_config.py --show-config --context myapp --environment dev
```

#### Connection Issues
```bash
# Test with minimal load first
python scripts/run_load_tests.py --test-type smoke --context myapp

# Check SSL settings
# Set verify_ssl: false in config for self-signed certificates
```

### Debug Mode
```bash
# Enable verbose logging
python scripts/run_load_tests.py --test-type load --context andoc --verbose

# PowerShell verbose mode
.\scripts\enhanced-load-test.ps1 -TestType load -Context andoc -Verbose
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the [troubleshooting guide](docs/troubleshooting.md)
- Review [examples](docs/examples/)
- Open an issue on GitHub
