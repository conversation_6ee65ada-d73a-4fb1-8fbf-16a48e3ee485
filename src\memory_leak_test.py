#!/usr/bin/env python3
"""
Memory leak detection during sustained load testing
"""
import asyncio
import psutil
import json
import time
import gc
from datetime import datetime
import argparse
import subprocess
import threading
from pathlib import Path

class MemoryLeakTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.memory_samples = []
        self.monitoring = True
        self.process = None

    async def run_memory_leak_test(self, users: int = 100, duration: int = 600):
        """Run sustained load test while monitoring memory usage"""
        print(f"🧠 Starting memory leak test: {users} users for {duration} seconds")
        
        # Start memory monitoring in background
        monitor_task = asyncio.create_task(self._monitor_memory(duration))
        
        # Start the load test
        load_test_task = asyncio.create_task(self._run_load_test(users, duration))
        
        # Wait for both to complete
        await asyncio.gather(monitor_task, load_test_task)
        
        # Analyze results
        await self._analyze_memory_usage()

    async def _monitor_memory(self, duration: int):
        """Monitor system and process memory usage"""
        start_time = time.time()
        sample_interval = 5  # seconds
        
        print("📊 Starting memory monitoring...")
        
        while self.monitoring and (time.time() - start_time) < duration:
            # System memory
            system_memory = psutil.virtual_memory()
            
            # Process memory (if we can find our test process)
            process_memory = None
            python_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cmdline']):
                try:
                    if 'python' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        if 'src.main' in cmdline:
                            python_processes.append({
                                'pid': proc.info['pid'],
                                'memory_mb': proc.info['memory_info'].rss / (1024 * 1024),
                                'cmdline': cmdline
                            })
            
            sample = {
                'timestamp': datetime.now().isoformat(),
                'elapsed_seconds': time.time() - start_time,
                'system_memory': {
                    'total_gb': system_memory.total / (1024**3),
                    'available_gb': system_memory.available / (1024**3),
                    'used_percent': system_memory.percent,
                    'free_gb': system_memory.free / (1024**3)
                },
                'python_processes': python_processes,
                'total_python_memory_mb': sum(p['memory_mb'] for p in python_processes)
            }
            
            self.memory_samples.append(sample)
            
            # Print current status
            if len(self.memory_samples) % 12 == 0:  # Every minute
                print(f"   Memory: System {system_memory.percent:.1f}%, "
                      f"Python processes: {sample['total_python_memory_mb']:.1f} MB")
            
            # Check for concerning memory growth
            if len(self.memory_samples) > 10:
                recent_growth = self._calculate_memory_growth()
                if recent_growth > 50:  # More than 50MB growth in recent samples
                    print(f"⚠️  Potential memory leak detected: {recent_growth:.1f} MB growth")
            
            await asyncio.sleep(sample_interval)

    def _calculate_memory_growth(self) -> float:
        """Calculate memory growth over recent samples"""
        if len(self.memory_samples) < 10:
            return 0.0
        
        recent_samples = self.memory_samples[-10:]
        first_memory = recent_samples[0]['total_python_memory_mb']
        last_memory = recent_samples[-1]['total_python_memory_mb']
        
        return last_memory - first_memory

    async def _run_load_test(self, users: int, duration: int):
        """Run the actual load test"""
        print(f"🚀 Starting load test with {users} users")
        
        # Modify config for this test
        test_config = self.config.copy()
        test_config['num_threads'] = users
        
        # Save temporary config
        temp_config_path = "temp_memory_test_config.json"
        with open(temp_config_path, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        try:
            # Run the load test
            cmd = [
                "python", "-m", "src.main",
                "-c", temp_config_path,
                "--skip-jwt-validation"
            ]
            
            print(f"Running command: {' '.join(cmd)}")
            
            # Start process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.process = process
            
            # Wait for specified duration or process completion
            try:
                stdout, stderr = process.communicate(timeout=duration + 60)
                print("✅ Load test completed normally")
                if stderr:
                    print(f"⚠️  Stderr: {stderr}")
            except subprocess.TimeoutExpired:
                print("⏰ Load test timed out, terminating...")
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            
        finally:
            # Clean up temp config
            Path(temp_config_path).unlink(missing_ok=True)
            self.monitoring = False

    async def _analyze_memory_usage(self):
        """Analyze memory usage patterns and detect leaks"""
        if not self.memory_samples:
            print("❌ No memory samples collected")
            return
        
        print(f"\n📈 Analyzing {len(self.memory_samples)} memory samples...")
        
        # Calculate statistics
        python_memory_values = [s['total_python_memory_mb'] for s in self.memory_samples]
        system_memory_values = [s['system_memory']['used_percent'] for s in self.memory_samples]
        
        analysis = {
            'test_duration_seconds': self.memory_samples[-1]['elapsed_seconds'],
            'python_memory': {
                'initial_mb': python_memory_values[0],
                'final_mb': python_memory_values[-1],
                'peak_mb': max(python_memory_values),
                'growth_mb': python_memory_values[-1] - python_memory_values[0],
                'average_mb': sum(python_memory_values) / len(python_memory_values)
            },
            'system_memory': {
                'initial_percent': system_memory_values[0],
                'final_percent': system_memory_values[-1],
                'peak_percent': max(system_memory_values),
                'average_percent': sum(system_memory_values) / len(system_memory_values)
            }
        }
        
        # Detect potential memory leaks
        memory_growth_rate = analysis['python_memory']['growth_mb'] / (analysis['test_duration_seconds'] / 60)  # MB per minute
        
        print(f"\n📊 MEMORY ANALYSIS RESULTS:")
        print(f"Test Duration: {analysis['test_duration_seconds']:.0f} seconds")
        print(f"Python Memory - Initial: {analysis['python_memory']['initial_mb']:.1f} MB")
        print(f"Python Memory - Final: {analysis['python_memory']['final_mb']:.1f} MB")
        print(f"Python Memory - Peak: {analysis['python_memory']['peak_mb']:.1f} MB")
        print(f"Python Memory - Growth: {analysis['python_memory']['growth_mb']:.1f} MB")
        print(f"Memory Growth Rate: {memory_growth_rate:.2f} MB/minute")
        
        # Leak detection
        if memory_growth_rate > 5:  # More than 5 MB per minute
            print(f"🚨 POTENTIAL MEMORY LEAK DETECTED!")
            print(f"   Growth rate: {memory_growth_rate:.2f} MB/minute is concerning")
        elif memory_growth_rate > 2:
            print(f"⚠️  Moderate memory growth detected: {memory_growth_rate:.2f} MB/minute")
        else:
            print(f"✅ Memory usage appears stable")
        
        # Save detailed results
        await self._save_results(analysis)

    async def _save_results(self, analysis: dict):
        """Save detailed memory test results"""
        filename = f"memory_leak_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'test_date': datetime.now().isoformat(),
            'analysis': analysis,
            'raw_samples': self.memory_samples
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📋 Memory leak test results saved to {filename}")

async def main():
    parser = argparse.ArgumentParser(description="Memory leak detection test")
    parser.add_argument("--users", type=int, default=100, help="Number of concurrent users")
    parser.add_argument("--duration", type=int, default=600, help="Test duration in seconds")
    parser.add_argument("--config", default="config.global.json", help="Configuration file path")
    
    args = parser.parse_args()
    
    tester = MemoryLeakTester(args.config)
    await tester.run_memory_leak_test(args.users, args.duration)

if __name__ == "__main__":
    asyncio.run(main())