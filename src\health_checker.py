async def pre_test_health_check(base_url: str, jwt_token: str) -> bool:
    """Comprehensive health check before starting load test"""
    checks = [
        ("API Health", f"{base_url}/health"),
        ("Authentication", f"{base_url}/auth/validate"),
        ("Database", f"{base_url}/db/health"),
        ("AOAI Connection", f"{base_url}/aoai/health")
    ]
    
    all_healthy = True
    for check_name, endpoint in checks:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(endpoint, 
                    headers={"X-CGPT-AUTHORIZATION": f"Bearer {jwt_token}"},
                    timeout=10.0)
                
                if response.status_code == 200:
                    print(f"✓ {check_name}: OK")
                else:
                    print(f"✗ {check_name}: HTTP {response.status_code}")
                    all_healthy = False
        except Exception as e:
            print(f"✗ {check_name}: {str(e)}")
            all_healthy = False
    
    return all_healthy