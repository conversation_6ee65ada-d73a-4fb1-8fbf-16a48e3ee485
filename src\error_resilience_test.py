#!/usr/bin/env python3
"""
Error Rate & Resilience Test - Inject faults and measure error handling
"""
import asyncio
import httpx
import json
import random
from datetime import datetime

class ErrorResilienceTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.base_url = self.config["base_url"]
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.config['jwt_token']}"}

    async def run_resilience_tests(self):
        """Run comprehensive resilience tests"""
        print("🚨 Starting error resilience tests...")
        
        tests = [
            ("Invalid JWT Handling", self._test_invalid_jwt),
            ("Malformed Request Handling", self._test_malformed_requests),
            ("Rate Limiting Behavior", self._test_rate_limiting),
            ("Timeout Handling", self._test_timeout_handling),
            ("Large Payload Handling", self._test_large_payloads)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 Testing {test_name}...")
            result = await test_func()
            results.append({"test_name": test_name, **result})
        
        await self._save_resilience_results(results)

    async def _test_invalid_jwt(self):
        """Test handling of invalid JWT tokens"""
        invalid_tokens = [
            "invalid.jwt.token",
            "",
            "Bearer malformed",
            "expired.token.here"
        ]
        
        results = []
        for token in invalid_tokens:
            headers = {"X-CGPT-AUTHORIZATION": f"Bearer {token}"}
            async with httpx.AsyncClient(timeout=10.0) as client:
                try:
                    response = await client.get(f"{self.base_url}/health", headers=headers)
                    results.append({
                        "token_type": "invalid",
                        "status_code": response.status_code,
                        "handles_gracefully": response.status_code in [401, 403]
                    })
                except Exception as e:
                    results.append({"token_type": "invalid", "error": str(e)})
        
        graceful_handling = sum(1 for r in results if r.get("handles_gracefully", False))
        return {"graceful_error_rate": (graceful_handling / len(results)) * 100}

if __name__ == "__main__":
    asyncio.run(ErrorResilienceTester().run_resilience_tests())