"""
Monitoring and Alerting Integrations
===================================

Provides integrations with external monitoring systems, alerting platforms,
and observability tools for comprehensive load testing monitoring.
"""

import json
import time
import logging
import requests
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@dataclass
class Alert:
    """Alert data structure"""
    name: str
    message: str
    severity: str
    timestamp: datetime
    metrics: Dict[str, Any]
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class MonitoringIntegration(ABC):
    """Abstract base class for monitoring integrations"""
    
    @abstractmethod
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics to monitoring system"""
        pass
    
    @abstractmethod
    def send_alert(self, alert: Alert) -> bool:
        """Send alert to monitoring system"""
        pass


class PrometheusIntegration(MonitoringIntegration):
    """Integration with Prometheus monitoring system"""
    
    def __init__(self, pushgateway_url: str, job_name: str = "locust_load_test"):
        self.pushgateway_url = pushgateway_url.rstrip('/')
        self.job_name = job_name
        self.instance_id = f"locust_{int(time.time())}"
        
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics to Prometheus Pushgateway"""
        try:
            # Convert metrics to Prometheus format
            prometheus_metrics = self._convert_to_prometheus_format(metrics)
            
            # Send to pushgateway
            url = f"{self.pushgateway_url}/metrics/job/{self.job_name}/instance/{self.instance_id}"
            
            response = requests.post(
                url,
                data=prometheus_metrics,
                headers={'Content-Type': 'text/plain'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.debug("Metrics sent to Prometheus successfully")
                return True
            else:
                logger.error(f"Failed to send metrics to Prometheus: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending metrics to Prometheus: {e}")
            return False
    
    def send_alert(self, alert: Alert) -> bool:
        """Send alert as metric to Prometheus"""
        try:
            alert_metrics = {
                f"locust_alert_{alert.name}": 1 if not alert.resolved else 0,
                "locust_alert_severity": self._severity_to_number(alert.severity)
            }
            
            return self.send_metrics(alert_metrics)
            
        except Exception as e:
            logger.error(f"Error sending alert to Prometheus: {e}")
            return False
    
    def _convert_to_prometheus_format(self, metrics: Dict[str, Any]) -> str:
        """Convert metrics dictionary to Prometheus format"""
        lines = []
        timestamp = int(time.time() * 1000)  # Prometheus expects milliseconds
        
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                # Clean metric name for Prometheus
                metric_name = f"locust_{key.replace('.', '_').replace('-', '_')}"
                lines.append(f"{metric_name} {value} {timestamp}")
        
        return '\n'.join(lines)
    
    def _severity_to_number(self, severity: str) -> int:
        """Convert severity string to number"""
        severity_map = {
            "info": 1,
            "warning": 2,
            "error": 3,
            "critical": 4
        }
        return severity_map.get(severity.lower(), 1)


class InfluxDBIntegration(MonitoringIntegration):
    """Integration with InfluxDB time series database"""
    
    def __init__(self, url: str, database: str, username: str = None, password: str = None):
        self.url = url.rstrip('/')
        self.database = database
        self.auth = (username, password) if username and password else None
        
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics to InfluxDB"""
        try:
            # Convert to InfluxDB line protocol
            lines = self._convert_to_influx_format(metrics)
            
            # Send to InfluxDB
            url = f"{self.url}/write?db={self.database}"
            
            response = requests.post(
                url,
                data='\n'.join(lines),
                auth=self.auth,
                timeout=10
            )
            
            if response.status_code == 204:
                logger.debug("Metrics sent to InfluxDB successfully")
                return True
            else:
                logger.error(f"Failed to send metrics to InfluxDB: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending metrics to InfluxDB: {e}")
            return False
    
    def send_alert(self, alert: Alert) -> bool:
        """Send alert to InfluxDB"""
        try:
            alert_data = {
                "alert_active": 1 if not alert.resolved else 0,
                "alert_severity": self._severity_to_number(alert.severity),
                **alert.metrics
            }
            
            return self.send_metrics(alert_data)
            
        except Exception as e:
            logger.error(f"Error sending alert to InfluxDB: {e}")
            return False
    
    def _convert_to_influx_format(self, metrics: Dict[str, Any]) -> List[str]:
        """Convert metrics to InfluxDB line protocol"""
        lines = []
        timestamp = int(time.time() * 1000000000)  # InfluxDB expects nanoseconds
        
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                line = f"locust_metrics,test=load_test {key}={value} {timestamp}"
                lines.append(line)
        
        return lines
    
    def _severity_to_number(self, severity: str) -> int:
        """Convert severity string to number"""
        severity_map = {
            "info": 1,
            "warning": 2,
            "error": 3,
            "critical": 4
        }
        return severity_map.get(severity.lower(), 1)


class SlackIntegration:
    """Integration with Slack for alert notifications"""
    
    def __init__(self, webhook_url: str, channel: str = None):
        self.webhook_url = webhook_url
        self.channel = channel
        
    def send_alert(self, alert: Alert) -> bool:
        """Send alert to Slack"""
        try:
            # Choose emoji based on severity
            emoji_map = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "critical": "🚨"
            }
            
            emoji = emoji_map.get(alert.severity.lower(), "📊")
            
            # Create Slack message
            message = {
                "text": f"{emoji} Load Test Alert: {alert.name}",
                "attachments": [
                    {
                        "color": self._severity_to_color(alert.severity),
                        "fields": [
                            {
                                "title": "Message",
                                "value": alert.message,
                                "short": False
                            },
                            {
                                "title": "Severity",
                                "value": alert.severity.upper(),
                                "short": True
                            },
                            {
                                "title": "Time",
                                "value": alert.timestamp.strftime("%Y-%m-%d %H:%M:%S UTC"),
                                "short": True
                            }
                        ]
                    }
                ]
            }
            
            if self.channel:
                message["channel"] = self.channel
            
            # Add metrics as fields if available
            if alert.metrics:
                for key, value in list(alert.metrics.items())[:5]:  # Limit to 5 metrics
                    message["attachments"][0]["fields"].append({
                        "title": key.replace('_', ' ').title(),
                        "value": str(value),
                        "short": True
                    })
            
            # Send to Slack
            response = requests.post(
                self.webhook_url,
                json=message,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.debug("Alert sent to Slack successfully")
                return True
            else:
                logger.error(f"Failed to send alert to Slack: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending alert to Slack: {e}")
            return False
    
    def _severity_to_color(self, severity: str) -> str:
        """Convert severity to Slack color"""
        color_map = {
            "info": "good",
            "warning": "warning", 
            "error": "danger",
            "critical": "danger"
        }
        return color_map.get(severity.lower(), "good")


class EmailIntegration:
    """Integration for email alert notifications"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, from_email: str, to_emails: List[str]):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
        
    def send_alert(self, alert: Alert) -> bool:
        """Send alert via email"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"Load Test Alert: {alert.name} [{alert.severity.upper()}]"
            
            # Create email body
            body = f"""
Load Test Alert Notification

Alert: {alert.name}
Severity: {alert.severity.upper()}
Message: {alert.message}
Time: {alert.timestamp.strftime("%Y-%m-%d %H:%M:%S UTC")}
Status: {'RESOLVED' if alert.resolved else 'ACTIVE'}

Metrics:
"""
            
            for key, value in alert.metrics.items():
                body += f"  {key}: {value}\n"
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            
            text = msg.as_string()
            server.sendmail(self.from_email, self.to_emails, text)
            server.quit()
            
            logger.debug("Alert sent via email successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error sending alert via email: {e}")
            return False


class WebhookIntegration:
    """Generic webhook integration for custom systems"""
    
    def __init__(self, webhook_url: str, headers: Dict[str, str] = None):
        self.webhook_url = webhook_url
        self.headers = headers or {}
        
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics via webhook"""
        try:
            payload = {
                "type": "metrics",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": metrics
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code in [200, 201, 202]:
                logger.debug("Metrics sent via webhook successfully")
                return True
            else:
                logger.error(f"Failed to send metrics via webhook: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending metrics via webhook: {e}")
            return False
    
    def send_alert(self, alert: Alert) -> bool:
        """Send alert via webhook"""
        try:
            payload = {
                "type": "alert",
                "alert": {
                    "name": alert.name,
                    "message": alert.message,
                    "severity": alert.severity,
                    "timestamp": alert.timestamp.isoformat(),
                    "resolved": alert.resolved,
                    "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None,
                    "metrics": alert.metrics
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code in [200, 201, 202]:
                logger.debug("Alert sent via webhook successfully")
                return True
            else:
                logger.error(f"Failed to send alert via webhook: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending alert via webhook: {e}")
            return False


class MonitoringManager:
    """Manages multiple monitoring integrations"""
    
    def __init__(self):
        self.integrations: List[MonitoringIntegration] = []
        self.alert_integrations: List[Any] = []  # Can include non-MonitoringIntegration classes
        
    def add_integration(self, integration: MonitoringIntegration):
        """Add a monitoring integration"""
        self.integrations.append(integration)
        logger.info(f"Added monitoring integration: {type(integration).__name__}")
        
    def add_alert_integration(self, integration: Any):
        """Add an alert integration"""
        self.alert_integrations.append(integration)
        logger.info(f"Added alert integration: {type(integration).__name__}")
        
    def send_metrics(self, metrics: Dict[str, Any]) -> Dict[str, bool]:
        """Send metrics to all integrations"""
        results = {}
        
        for integration in self.integrations:
            try:
                success = integration.send_metrics(metrics)
                results[type(integration).__name__] = success
            except Exception as e:
                logger.error(f"Error in {type(integration).__name__}: {e}")
                results[type(integration).__name__] = False
                
        return results
        
    def send_alert(self, alert: Alert) -> Dict[str, bool]:
        """Send alert to all alert integrations"""
        results = {}
        
        # Send to monitoring integrations that support alerts
        for integration in self.integrations:
            if hasattr(integration, 'send_alert'):
                try:
                    success = integration.send_alert(alert)
                    results[type(integration).__name__] = success
                except Exception as e:
                    logger.error(f"Error in {type(integration).__name__}: {e}")
                    results[type(integration).__name__] = False
        
        # Send to dedicated alert integrations
        for integration in self.alert_integrations:
            try:
                success = integration.send_alert(alert)
                results[type(integration).__name__] = success
            except Exception as e:
                logger.error(f"Error in {type(integration).__name__}: {e}")
                results[type(integration).__name__] = False
                
        return results
