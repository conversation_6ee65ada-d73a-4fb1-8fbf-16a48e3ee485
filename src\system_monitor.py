#!/usr/bin/env python3
import asyncio
import psutil
import json
import time
from datetime import datetime
import aiofiles

class SystemMonitor:
    def __init__(self):
        self.metrics = []
        self.monitoring = True
    
    async def start_monitoring(self, duration: int = 3600):
        """Monitor system for specified duration"""
        start_time = time.time()
        
        while self.monitoring and (time.time() - start_time) < duration:
            metrics = await self._collect_metrics()
            self.metrics.append(metrics)
            
            # Alert on high resource usage
            if metrics["cpu_percent"] > 80:
                print(f"⚠️  HIGH CPU: {metrics['cpu_percent']:.1f}%")
            
            if metrics["memory_percent"] > 85:
                print(f"⚠️  HIGH MEMORY: {metrics['memory_percent']:.1f}%")
            
            if metrics["disk_io_read_mb"] > 100:
                print(f"⚠️  HIGH DISK I/O: {metrics['disk_io_read_mb']:.1f} MB/s")
            
            await asyncio.sleep(5)
        
        await self._save_metrics()
    
    async def _collect_metrics(self):
        """Collect comprehensive system metrics"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        network = psutil.net_io_counters()
        
        # Process information
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                if proc.info['cpu_percent'] > 5 or proc.info['memory_percent'] > 5:
                    processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": cpu_percent,
            "cpu_count": psutil.cpu_count(),
            "memory_percent": memory.percent,
            "memory_total_gb": memory.total / (1024**3),
            "memory_available_gb": memory.available / (1024**3),
            "disk_percent": (disk.used / disk.total) * 100,
            "disk_free_gb": disk.free / (1024**3),
            "disk_io_read_mb": disk_io.read_bytes / (1024**2) if disk_io else 0,
            "disk_io_write_mb": disk_io.write_bytes / (1024**2) if disk_io else 0,
            "network_sent_mb": network.bytes_sent / (1024**2),
            "network_recv_mb": network.bytes_recv / (1024**2),
            "active_connections": len(psutil.net_connections()),
            "top_processes": processes[:10]
        }
    
    async def _save_metrics(self):
        """Save collected metrics to file"""
        filename = f"system_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        async with aiofiles.open(filename, 'w') as f:
            await f.write(json.dumps(self.metrics, indent=2))
        
        print(f"📊 System metrics saved to {filename}")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--duration", type=int, default=3600, help="Monitoring duration in seconds")
    args = parser.parse_args()
    
    monitor = SystemMonitor()
    asyncio.run(monitor.start_monitoring(args.duration))