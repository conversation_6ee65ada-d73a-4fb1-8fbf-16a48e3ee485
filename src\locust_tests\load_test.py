"""
Standard Load Test
=================

Tests the application under expected normal load conditions.
Simulates realistic user behavior patterns with proper wait times.
"""

import os
import json
import random
from locust import task, between
from src.locust_framework import AuthenticatedUser, ScenarioUser


class LoadTestUser(AuthenticatedUser):
    """Standard load test user with realistic behavior patterns"""
    
    wait_time = between(1, 5)  # Realistic user think time
    weight = 3  # Higher weight for normal operations
    
    def on_start(self):
        super().on_start()
        
        # Load configuration
        config_path = os.environ.get("CONFIG_PATH", "config.global.json")
        with open(config_path, 'r') as f:
            self.config_data = json.load(f)
        
        self.endpoints = self.config_data.get("endpoints", [])
        self.contexts = ["andoc", "talperftraitement"]
        
        # Create sessions for different contexts
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
    
    @task(5)
    def health_check(self):
        """Regular health check - most common operation"""
        with self.authenticated_request("GET", "/health", name="health_check") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(10)
    def send_message(self):
        """Send a message - core functionality"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        messages = [
            "Bonjour, comment puis-je vous aider?",
            "J'ai une question sur les congés",
            "Pouvez-vous m'expliquer la politique de télétravail?",
            "Comment faire une demande de formation?",
            "Quels sont mes avantages sociaux?",
            "Comment mettre à jour mes informations personnelles?",
            "J'ai besoin d'aide avec ma fiche de paie",
            "Quelle est la procédure pour les notes de frais?"
        ]
        
        message = random.choice(messages)
        endpoint = f"/{self.active_context}/chat/send"
        
        payload = {
            "message": message,
            "session_id": self.session_manager.session_data.session_id
        }
        
        with self.authenticated_request("POST", endpoint, json=payload, 
                                      name=f"{self.active_context}_message") as response:
            if response.status_code == 200:
                response.success()
                # Simulate reading response
                self.wait_time = between(2, 8)  # Longer wait after getting response
            else:
                response.failure(f"Message failed: {response.status_code}")
    
    @task(3)
    def get_chat_history(self):
        """Retrieve chat history"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            return
        
        endpoint = f"/{self.active_context}/chat/history"
        params = {
            "session_id": self.session_manager.session_data.session_id,
            "limit": 10
        }
        
        with self.authenticated_request("GET", endpoint, params=params,
                                      name=f"{self.active_context}_history") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"History retrieval failed: {response.status_code}")
    
    @task(2)
    def create_new_session(self):
        """Occasionally create a new session (context switching)"""
        new_context = random.choice(self.contexts)
        if new_context != self.active_context:
            self.active_context = new_context
            self.create_user_session(new_context)
    
    @task(1)
    def upload_document(self):
        """Simulate document upload (less frequent)"""
        endpoint = f"/{self.active_context}/document/upload"
        
        # Simulate file upload with form data
        files = {"file": ("test_document.txt", "This is a test document", "text/plain")}
        data = {"session_id": getattr(self.session_manager.session_data, 'session_id', '')}
        
        with self.authenticated_request("POST", endpoint, files=files, data=data,
                                      name=f"{self.active_context}_upload") as response:
            if response.status_code in [200, 201]:
                response.success()
            else:
                response.failure(f"Upload failed: {response.status_code}")


class ScenarioBasedUser(ScenarioUser):
    """User that follows predefined scenarios from configuration"""
    
    wait_time = between(2, 6)
    weight = 1  # Lower weight for scenario-based testing
    
    def on_start(self):
        super().on_start()
        
        # Define common scenarios
        self.scenarios = [
            {
                "name": "employee_onboarding",
                "endpoints": [
                    {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "session_id", "valueFrom": "session_id"}]},
                    {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Je commence lundi, que dois-je faire?", "session_id": "{session_id}"}},
                    {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Comment accéder au portail employé?", "session_id": "{session_id}"}},
                    {"url": "/andoc/chat/history", "method": "GET", "params": {"session_id": "{session_id}"}}
                ]
            },
            {
                "name": "leave_request",
                "endpoints": [
                    {"url": "/talperftraitement/session/new", "method": "POST", "extract": [{"key": "session_id", "valueFrom": "session_id"}]},
                    {"url": "/talperftraitement/rag/message", "method": "POST", "data": {"message": "Comment demander des congés?", "session_id": "{session_id}"}},
                    {"url": "/talperftraitement/rag/message", "method": "POST", "data": {"message": "Qui approuve mes demandes de congés?", "session_id": "{session_id}"}},
                ]
            },
            {
                "name": "benefits_inquiry",
                "endpoints": [
                    {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "session_id", "valueFrom": "session_id"}]},
                    {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Quels sont mes avantages sociaux?", "session_id": "{session_id}"}},
                    {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Comment modifier mes bénéficiaires?", "session_id": "{session_id}"}},
                ]
            }
        ]
    
    @task
    def execute_random_scenario(self):
        """Execute a random scenario"""
        if self.scenarios:
            scenario = random.choice(self.scenarios)
            self.run_scenario(scenario)


class AdminUser(AuthenticatedUser):
    """Simulates administrative users with different access patterns"""
    
    wait_time = between(5, 15)  # Admins typically spend more time per action
    weight = 1  # Lower frequency
    
    @task(3)
    def system_health_check(self):
        """Admin health monitoring"""
        endpoints = ["/health", "/metrics", "/status"]
        
        for endpoint in endpoints:
            with self.authenticated_request("GET", endpoint, 
                                          name=f"admin_{endpoint.replace('/', '')}") as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"Admin check failed: {response.status_code}")
    
    @task(2)
    def user_management(self):
        """Simulate user management operations"""
        operations = [
            {"url": "/admin/users", "method": "GET", "name": "list_users"},
            {"url": "/admin/sessions", "method": "GET", "name": "active_sessions"},
            {"url": "/admin/logs", "method": "GET", "params": {"limit": 50}, "name": "system_logs"}
        ]
        
        operation = random.choice(operations)
        method = operation["method"]
        url = operation["url"]
        name = operation["name"]
        params = operation.get("params", {})
        
        with self.authenticated_request(method, url, params=params, name=name) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Admin operation failed: {response.status_code}")
    
    @task(1)
    def configuration_check(self):
        """Check system configuration"""
        with self.authenticated_request("GET", "/admin/config", 
                                      name="admin_config") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Config check failed: {response.status_code}")


# Define user classes for Locust to pick up
user_classes = [LoadTestUser, ScenarioBasedUser, AdminUser]
