[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "test-charge"
version = "0.1.0"
description = "Enhanced Locust Load Testing Framework for HTTP APIs"
readme = "docs/README.md"
requires-python = ">=3.9"
authors = [
    {name = "Load Testing Team", email = "<EMAIL>"},
]
keywords = ["load testing", "performance testing", "locust", "api testing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Testing",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",

    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Core Dependencies
    "locust>=2.17.0",
    "requests>=2.31.0",
    "httpx>=0.25.0",

    # Configuration and Data Handling
    "pydantic>=2.5.0",
    "pyyaml>=6.0.1",
    "python-dotenv>=1.0.0",

    # JWT Token Handling
    "pyjwt>=2.8.0",
    "cryptography>=41.0.0",

    # System Monitoring
    "psutil>=5.9.0",

    # Data Analysis and Reporting
    "pandas>=2.1.0",
    "numpy>=1.24.0",

    # Visualization (for enhanced HTML reports)
    "plotly>=5.17.0",
    "jinja2>=3.1.0",

    # Async Support
    "aiohttp>=3.9.0",
    "aiofiles>=23.2.1",
]

[dependency-groups]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
    "uvicorn>=0.34.2",
]

monitoring = [
    "influxdb-client>=1.38.0",
    "prometheus-client>=0.19.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
]

[project.scripts]
# Enhanced load testing commands
enhanced-locust = "scripts.run_load_tests:main"
locust-config = "scripts.manage_config:main"
locust-monitor = "scripts.setup_monitoring:main"

# Legacy compatibility
test-charge = "src.main:main"

[tool.uv]
package = true

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
