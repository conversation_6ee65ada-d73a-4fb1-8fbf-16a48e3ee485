# Enhanced Locust File using the new framework
# locust -f src/locustfile.py --host=https://mia-ta.lacaisse.com

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from locust_framework import Authenticated<PERSON><PERSON>, Scenario<PERSON>ser, ConfigManager, ReportManager
from locust import events

# Initialize enhanced reporting
report_manager = ReportManager()

# Load configuration
config_manager = ConfigManager()
context = os.environ.get("CONFIG_CONTEXT", "andoc")
environment = os.environ.get("CONFIG_ENVIRONMENT")

try:
    config = config_manager.load_config(context=context, environment=environment)

    # Set global configuration for user classes
    os.environ["JWT_TOKEN"] = config.jwt_token
    os.environ["VERIFY_SSL"] = str(config.verify_ssl).lower()

except Exception as e:
    print(f"Error loading configuration: {e}")
    # Fallback to basic configuration
    config = None


class EnhancedAPIUser(AuthenticatedUser):
    """Enhanced API user with improved functionality"""

    weight = 3

    def on_start(self):
        super().on_start()

        # Set configuration from global config
        if hasattr(self.environment, 'config'):
            self.config = self.environment.config
        elif config:
            self.config = config
        else:
            # Fallback configuration
            import json
            config_path = os.environ.get("CONFIG_PATH", "config.global.json")
            with open(config_path, "r", encoding="utf-8") as f:
                config_data = json.load(f)
            self.endpoints = config_data.get("endpoints", [])
            return

        self.endpoints = self.config.endpoints if hasattr(self.config, 'endpoints') else []

    @task
    def run_endpoint_sequence(self):
        """Run through configured endpoints"""
        for endpoint in self.endpoints:
            url = endpoint.get("url", "/")
            method = endpoint.get("method", "GET").upper()
            data = endpoint.get("data", {})
            endpoint_id = endpoint.get("id", url)

            try:
                if method == "GET":
                    self.authenticated_request("GET", url, params=data, name=endpoint_id)
                elif method == "POST":
                    self.authenticated_request("POST", url, json=data, name=endpoint_id)
                elif method == "PUT":
                    self.authenticated_request("PUT", url, json=data, name=endpoint_id)
                elif method == "DELETE":
                    self.authenticated_request("DELETE", url, json=data, name=endpoint_id)

            except Exception as e:
                print(f"Exception lors de l'appel à {url}: {e}")


class LegacyCompatibilityUser(EnhancedAPIUser):
    """Maintains compatibility with existing test configurations"""

    weight = 1

    def _handle_response(self, response, endpoint):
        """Legacy response handler for backward compatibility"""
        if response.status_code >= 400:
            response.failure(f"HTTP {response.status_code}: {response.text}")
        else:
            response.success()


# Event listeners for enhanced logging and reporting
@events.request.add_listener
def enhanced_request_logging(request_type, name, response_time, response_length,
                           response, context, exception, **kwargs):
    """Enhanced request logging with better error tracking"""

    # Log errors to file (maintain backward compatibility)
    if exception or (response and response.status_code >= 400):
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        with open(log_dir / "errors.log", "a", encoding="utf-8") as f:
            timestamp = events.datetime.now().isoformat()
            status = response.status_code if response else 'ERR'
            error_msg = str(exception) if exception else response.text[:100] if response else 'Unknown error'
            f.write(f"[{timestamp}] {name} {status} {error_msg}\n")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Initialize test environment"""
    print("🚀 Enhanced load test starting...")

    # Store configuration in environment for user access
    if config:
        environment.config = config
        environment.jwt_token = config.jwt_token
        environment.verify_ssl = config.verify_ssl


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Test completion handler"""
    print("🏁 Enhanced load test completed!")


# Define user classes for Locust to discover
user_classes = [EnhancedAPIUser, LegacyCompatibilityUser]