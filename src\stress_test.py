#!/usr/bin/env python3
"""
Stress test - Push system beyond normal capacity to find breaking point
"""
import asyncio
import subprocess
import json
import time
import psutil
from datetime import datetime
import argparse

class StressTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.results = []

    async def run_stress_test(self, max_users: int = 3000):
        """Run stress test beyond normal capacity"""
        print(f"💥 Starting stress test up to {max_users} users")
        
        # Stress test phases
        stress_levels = [2000, 2500, 3000, 3500, 4000]
        
        for users in stress_levels:
            if users > max_users:
                break
                
            print(f"\n🔥 Stress testing with {users} users...")
            
            # Monitor system before stress
            pre_metrics = self._get_system_metrics()
            
            # Run stress test
            start_time = time.time()
            success_rate, error_details = await self._run_stress_load(users)
            duration = time.time() - start_time
            
            # Monitor system after stress
            post_metrics = self._get_system_metrics()
            
            result = {
                "users": users,
                "success_rate": success_rate,
                "duration": duration,
                "pre_metrics": pre_metrics,
                "post_metrics": post_metrics,
                "error_details": error_details,
                "timestamp": datetime.now().isoformat()
            }
            
            self.results.append(result)
            
            print(f"📊 {users} users: {success_rate:.1f}% success rate")
            print(f"   CPU: {pre_metrics['cpu']}% → {post_metrics['cpu']}%")
            print(f"   Memory: {pre_metrics['memory']}% → {post_metrics['memory']}%")
            
            # Stop if system becomes unstable
            if success_rate < 50 or post_metrics['memory'] > 95:
                print(f"🚨 System unstable at {users} users. Stopping stress test.")
                break
            
            # Recovery time between stress levels
            await asyncio.sleep(60)
        
        await self._save_stress_results()

    async def _run_stress_load(self, users: int):
        """Run load test with specified users"""
        # Create temporary config
        stress_config = self.config.copy()
        stress_config['num_threads'] = users
        
        temp_config = f"temp_stress_{users}.json"
        with open(temp_config, 'w') as f:
            json.dump(stress_config, f, indent=2)
        
        try:
            cmd = [
                "python", "-m", "src.main",
                "-c", temp_config,
                "--skip-jwt-validation"
            ]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=600  # 10 minutes max
            )
            
            # Parse results
            success_rate = self._parse_success_rate(result.stdout)
            error_details = self._parse_errors(result.stderr)
            
            return success_rate, error_details
            
        except subprocess.TimeoutExpired:
            return 0.0, ["Test timed out"]
        except Exception as e:
            return 0.0, [str(e)]
        finally:
            # Cleanup
            import os
            if os.path.exists(temp_config):
                os.remove(temp_config)

    def _get_system_metrics(self):
        """Get current system metrics"""
        return {
            "cpu": psutil.cpu_percent(interval=1),
            "memory": psutil.virtual_memory().percent,
            "disk": psutil.disk_usage('/').percent,
            "connections": len(psutil.net_connections())
        }

    def _parse_success_rate(self, output: str) -> float:
        """Parse success rate from output"""
        # Implementation depends on your output format
        import re
        match = re.search(r'(\d+\.?\d*)%.*success', output, re.IGNORECASE)
        return float(match.group(1)) if match else 0.0

    def _parse_errors(self, stderr: str) -> list:
        """Parse error details from stderr"""
        errors = []
        if stderr:
            errors = stderr.split('\n')[:10]  # First 10 errors
        return errors

    async def _save_stress_results(self):
        """Save stress test results"""
        filename = f"stress_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "test_type": "stress_test",
            "test_date": datetime.now().isoformat(),
            "results": self.results,
            "summary": {
                "max_stable_users": max([r["users"] for r in self.results if r["success_rate"] >= 95], default=0),
                "breaking_point": min([r["users"] for r in self.results if r["success_rate"] < 50], default="Not reached")
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📋 Stress test results saved to {filename}")

async def main():
    parser = argparse.ArgumentParser(description="Stress test beyond normal capacity")
    parser.add_argument("--users", type=int, default=3000, help="Maximum users to test")
    parser.add_argument("--config", default="config.global.json", help="Configuration file")
    
    args = parser.parse_args()
    
    tester = StressTester(args.config)
    await tester.run_stress_test(args.users)

if __name__ == "__main__":
    asyncio.run(main())