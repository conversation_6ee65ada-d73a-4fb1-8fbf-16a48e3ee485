"""
Smoke Test
==========

Basic functionality verification test.
Quick test to ensure the system is operational before running more intensive tests.
"""

import os
import json
from locust import task, between
from src.locust_framework import AuthenticatedUser


class SmokeTestUser(AuthenticatedUser):
    """Basic smoke test user for system verification"""
    
    wait_time = between(2, 5)
    weight = 1
    
    def on_start(self):
        super().on_start()
        
        # Load configuration
        config_path = os.environ.get("CONFIG_PATH", "config.global.json")
        with open(config_path, 'r') as f:
            self.config_data = json.load(f)
        
        self.contexts = ["andoc", "talperftraitement"]
        self.test_results = {
            "health_check": False,
            "authentication": False,
            "session_creation": False,
            "basic_messaging": False,
            "context_switching": False
        }
    
    @task(10)
    def health_check_verification(self):
        """Verify basic health endpoint"""
        with self.authenticated_request("GET", "/health", name="smoke_health") as response:
            if response.status_code == 200:
                self.test_results["health_check"] = True
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(8)
    def authentication_verification(self):
        """Verify JWT authentication is working"""
        # Test with valid token
        with self.authenticated_request("GET", "/user/profile", name="smoke_auth_valid") as response:
            if response.status_code == 200:
                self.test_results["authentication"] = True
                response.success()
            elif response.status_code == 401:
                response.failure("Authentication failed - invalid token")
            else:
                response.failure(f"Unexpected auth response: {response.status_code}")
    
    @task(6)
    def session_creation_verification(self):
        """Verify session creation works for all contexts"""
        for context in self.contexts:
            endpoint = f"/{context}/session/new"
            
            with self.authenticated_request("POST", endpoint, 
                                          name=f"smoke_session_{context}") as response:
                if response.status_code == 200:
                    self.test_results["session_creation"] = True
                    response.success()
                    
                    # Verify session data
                    try:
                        data = response.json()
                        if "session_id" in data:
                            response.success()
                        else:
                            response.failure("Session created but no session_id returned")
                    except:
                        response.failure("Session response not valid JSON")
                else:
                    response.failure(f"Session creation failed: {response.status_code}")
    
    @task(5)
    def basic_messaging_verification(self):
        """Verify basic messaging functionality"""
        # Create session first
        context = self.contexts[0]  # Use first context
        session_endpoint = f"/{context}/session/new"
        
        with self.authenticated_request("POST", session_endpoint, 
                                      name="smoke_session_for_message") as session_response:
            if session_response.status_code == 200:
                try:
                    session_data = session_response.json()
                    session_id = session_data.get("session_id")
                    
                    if session_id:
                        # Send test message
                        message_endpoint = f"/{context}/chat/send"
                        payload = {
                            "message": "Test message for smoke test",
                            "session_id": session_id
                        }
                        
                        with self.authenticated_request("POST", message_endpoint, json=payload,
                                                      name="smoke_basic_message") as message_response:
                            if message_response.status_code == 200:
                                self.test_results["basic_messaging"] = True
                                message_response.success()
                            else:
                                message_response.failure(f"Message failed: {message_response.status_code}")
                    else:
                        session_response.failure("No session_id in response")
                except:
                    session_response.failure("Invalid session response format")
            else:
                session_response.failure(f"Session creation failed: {session_response.status_code}")
    
    @task(3)
    def context_switching_verification(self):
        """Verify switching between contexts works"""
        sessions = {}
        
        # Create sessions in both contexts
        for context in self.contexts:
            endpoint = f"/{context}/session/new"
            
            with self.authenticated_request("POST", endpoint, 
                                          name=f"smoke_context_switch_{context}") as response:
                if response.status_code == 200:
                    try:
                        data = response.json()
                        sessions[context] = data.get("session_id")
                        response.success()
                    except:
                        response.failure("Invalid session response")
                else:
                    response.failure(f"Context switch session failed: {response.status_code}")
        
        # Test messaging in both contexts
        if len(sessions) == len(self.contexts):
            self.test_results["context_switching"] = True
            
            for context, session_id in sessions.items():
                message_endpoint = f"/{context}/chat/send"
                payload = {
                    "message": f"Context switch test for {context}",
                    "session_id": session_id
                }
                
                with self.authenticated_request("POST", message_endpoint, json=payload,
                                              name=f"smoke_context_message_{context}") as response:
                    if response.status_code == 200:
                        response.success()
                    else:
                        response.failure(f"Context message failed: {response.status_code}")
    
    @task(2)
    def endpoints_availability_check(self):
        """Check availability of key endpoints"""
        key_endpoints = [
            ("/health", "GET"),
            ("/status", "GET"),
            ("/version", "GET"),
            ("/metrics", "GET")
        ]
        
        for endpoint, method in key_endpoints:
            with self.authenticated_request(method, endpoint, 
                                          name=f"smoke_endpoint_{endpoint.replace('/', '')}") as response:
                if response.status_code in [200, 404]:  # 404 is acceptable for optional endpoints
                    response.success()
                else:
                    response.failure(f"Endpoint {endpoint} failed: {response.status_code}")
    
    @task(1)
    def error_handling_verification(self):
        """Verify proper error handling"""
        # Test invalid endpoint
        with self.authenticated_request("GET", "/nonexistent/endpoint", 
                                      name="smoke_error_404") as response:
            if response.status_code == 404:
                response.success()  # Expected 404
            else:
                response.failure(f"Expected 404 but got: {response.status_code}")
        
        # Test invalid method
        with self.authenticated_request("DELETE", "/health", 
                                      name="smoke_error_method") as response:
            if response.status_code in [405, 404]:  # Method not allowed or not found
                response.success()
            else:
                response.failure(f"Expected 405/404 but got: {response.status_code}")
        
        # Test malformed request
        with self.authenticated_request("POST", "/andoc/chat/send", 
                                      json={"invalid": "payload"},
                                      name="smoke_error_malformed") as response:
            if response.status_code >= 400:
                response.success()  # Expected error
            else:
                response.failure(f"Expected error but got: {response.status_code}")


class CriticalPathUser(AuthenticatedUser):
    """Tests the most critical user journey"""
    
    wait_time = between(3, 7)
    weight = 1
    
    def on_start(self):
        super().on_start()
        self.critical_path_success = True
    
    @task
    def critical_user_journey(self):
        """Execute the most critical user journey end-to-end"""
        # Step 1: Health check
        with self.authenticated_request("GET", "/health", name="critical_health") as response:
            if response.status_code != 200:
                self.critical_path_success = False
                response.failure("Critical path: Health check failed")
                return
            response.success()
        
        # Step 2: Create session
        with self.authenticated_request("POST", "/andoc/session/new", 
                                      name="critical_session") as response:
            if response.status_code != 200:
                self.critical_path_success = False
                response.failure("Critical path: Session creation failed")
                return
            
            try:
                session_data = response.json()
                session_id = session_data.get("session_id")
                if not session_id:
                    self.critical_path_success = False
                    response.failure("Critical path: No session ID returned")
                    return
                response.success()
            except:
                self.critical_path_success = False
                response.failure("Critical path: Invalid session response")
                return
        
        # Step 3: Send message
        payload = {
            "message": "Critical path test message",
            "session_id": session_id
        }
        
        with self.authenticated_request("POST", "/andoc/chat/send", json=payload,
                                      name="critical_message") as response:
            if response.status_code != 200:
                self.critical_path_success = False
                response.failure("Critical path: Message sending failed")
                return
            response.success()
        
        # Step 4: Get history
        params = {"session_id": session_id, "limit": 10}
        
        with self.authenticated_request("GET", "/andoc/chat/history", params=params,
                                      name="critical_history") as response:
            if response.status_code != 200:
                self.critical_path_success = False
                response.failure("Critical path: History retrieval failed")
                return
            response.success()
        
        # If we get here, critical path succeeded
        if self.critical_path_success:
            # Log success (in a real implementation, you might send this to monitoring)
            pass


# Define user classes for smoke testing
user_classes = [SmokeTestUser, CriticalPathUser]
