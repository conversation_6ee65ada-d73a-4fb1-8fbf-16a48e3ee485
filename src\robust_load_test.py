#!/usr/bin/env python3
import asyncio
import argparse
import json
import time
from datetime import datetime
from pathlib import Path
import psutil
import aiofiles

class RobustLoadTester:
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.results = []
        self.system_metrics = []
        
    async def run_capacity_test(self, target_users: int = 2000):
        """Test capacity with gradual ramp-up to target users"""
        ramp_steps = [50, 100, 250, 500, 750, 1000, 1250, 1500, 1750, 2000]
        
        for step_users in ramp_steps:
            if step_users > target_users:
                step_users = target_users
                
            print(f"\n🚀 Testing with {step_users} concurrent users...")
            
            # Monitor system before test
            await self._log_system_metrics(f"before_{step_users}")
            
            # Run test
            start_time = time.time()
            success_rate = await self._run_load_test(step_users)
            duration = time.time() - start_time
            
            # Monitor system after test
            await self._log_system_metrics(f"after_{step_users}")
            
            result = {
                "users": step_users,
                "success_rate": success_rate,
                "duration": duration,
                "timestamp": datetime.now().isoformat()
            }
            self.results.append(result)
            
            print(f"✅ {step_users} users: {success_rate:.1f}% success rate in {duration:.1f}s")
            
            # Stop if success rate drops below 95%
            if success_rate < 95.0:
                print(f"⚠️  Success rate dropped to {success_rate:.1f}%. Stopping ramp-up.")
                break
                
            # Cool down between tests
            await asyncio.sleep(30)
            
            if step_users >= target_users:
                break
        
        await self._generate_capacity_report()

    async def _run_load_test(self, num_users: int) -> float:
        """Run load test with specified number of users"""
        import subprocess
        
        cmd = [
            "python", "-m", "src.main", 
            "-c", self.config_path,
            "--skip-jwt-validation"
        ]
        
        # Modify config temporarily for this test
        await self._update_config_threads(num_users)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            # Parse success rate from output (you'll need to modify main.py to output this)
            success_rate = self._parse_success_rate(result.stdout)
            return success_rate
            
        except subprocess.TimeoutExpired:
            print(f"⚠️  Test with {num_users} users timed out")
            return 0.0
        except Exception as e:
            print(f"❌ Error running test: {e}")
            return 0.0

    async def _update_config_threads(self, num_threads: int):
        """Temporarily update config with new thread count"""
        async with aiofiles.open(self.config_path, 'r') as f:
            config = json.loads(await f.read())
        
        config['num_threads'] = num_threads
        
        async with aiofiles.open(self.config_path, 'w') as f:
            await f.write(json.dumps(config, indent=2))

    def _parse_success_rate(self, output: str) -> float:
        """Parse success rate from test output"""
        # This needs to be implemented based on your output format
        lines = output.split('\n')
        for line in lines:
            if 'success rate' in line.lower():
                # Extract percentage
                import re
                match = re.search(r'(\d+\.?\d*)%', line)
                if match:
                    return float(match.group(1))
        return 100.0  # Default if not found

    async def _log_system_metrics(self, phase: str):
        """Log system resource usage"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        network = psutil.net_io_counters()
        
        metrics = {
            "phase": phase,
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": memory.available / (1024**3),
            "disk_free_gb": disk.free / (1024**3),
            "network_sent_mb": network.bytes_sent / (1024**2),
            "network_recv_mb": network.bytes_recv / (1024**2)
        }
        
        self.system_metrics.append(metrics)
        print(f"📊 System: CPU {cpu_percent}%, RAM {memory.percent}%")

    async def _generate_capacity_report(self):
        """Generate comprehensive capacity report"""
        report = {
            "test_summary": {
                "target_users": 2000,
                "max_successful_users": max([r["users"] for r in self.results if r["success_rate"] >= 95], default=0),
                "test_date": datetime.now().isoformat()
            },
            "results": self.results,
            "system_metrics": self.system_metrics
        }
        
        async with aiofiles.open("capacity_test_report.json", "w") as f:
            await f.write(json.dumps(report, indent=2))
        
        print(f"\n📋 Capacity report saved to capacity_test_report.json")